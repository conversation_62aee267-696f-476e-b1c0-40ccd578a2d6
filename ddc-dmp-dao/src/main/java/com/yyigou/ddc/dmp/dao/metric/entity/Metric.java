package com.yyigou.ddc.dmp.dao.metric.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dmp_metric")
public class Metric {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String enterpriseNo;
    private String metricNo;
    private String metricCode;
    private String metricName;
    private String unit;
    private String numPrecision;
    private String displayFormat;
    private String description;
    private String datasetNo;
    private String datasetFieldNo;
    private String aggType;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}
