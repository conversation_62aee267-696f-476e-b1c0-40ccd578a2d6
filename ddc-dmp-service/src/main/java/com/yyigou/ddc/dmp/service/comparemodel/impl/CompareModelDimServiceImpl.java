package com.yyigou.ddc.dmp.service.comparemodel.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.enums.CompareModelDimTypeEnum;
import com.yyigou.ddc.dmp.common.enums.ComparePlanFieldSourceTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelDim;
import com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelDimMapper;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelRefFieldReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanRefFieldReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefFieldRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelDimService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Service
public class CompareModelDimServiceImpl extends DmpServiceImpl<CompareModelDimMapper, CompareModelDim> implements CompareModelDimService {

    /**
     * 保存比对对象/比对维度
     * @param compareModelSaveReq
     * @param compareModel
     * @param existingDimMap 已存在的维度映射，为null时表示新增场景
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveModelRefDim(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelDim> existingDimMap) {
        if (existingDimMap == null) {
            existingDimMap = new HashMap<>();
        }

        List<CompareModelDim> saveList = new ArrayList<>();
        List<CompareModelDim> updateList = new ArrayList<>();

        List<CompareModelDim> compareModelDimList = new ArrayList<>();
        int sort = 1;
        for (CompareModelRefFieldReq refFieldReq : compareModelSaveReq.getCompareObjectList()) {
            CompareModelDim compareModelDim = BeanCopyUtil.copyFields(refFieldReq, CompareModelDim.class);
            compareModelDim.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
            compareModelDim.setCompareModelNo(compareModel.getModelNo());
            compareModelDim.setSort(sort++);
            compareModelDim.setDimType(CompareModelDimTypeEnum.COMPARE_OBJECT.getValue());
            compareModelDim.setDatasetNo(refFieldReq.getDatasetNo());
            compareModelDim.setFieldCode(refFieldReq.getFieldCode());
            compareModelDimList.add(compareModelDim);
        }

        sort = 1;
        for (CompareModelRefFieldReq refFieldReq : compareModelSaveReq.getCompareDimList()) {
            CompareModelDim compareModelDim = BeanCopyUtil.copyFields(refFieldReq, CompareModelDim.class);
            compareModelDim.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
            compareModelDim.setCompareModelNo(compareModel.getModelNo());
            compareModelDim.setSort(sort++);
            compareModelDim.setDimType(CompareModelDimTypeEnum.COMPARE_DIM.getValue());
            compareModelDim.setDatasetNo(refFieldReq.getDatasetNo());
            compareModelDim.setFieldCode(refFieldReq.getFieldCode());
            compareModelDimList.add(compareModelDim);
        }

        // 处理保存或更新逻辑
        for (CompareModelDim dim : compareModelDimList) {
            if (StrUtil.isNotEmpty(dim.getCompareModelDimNo()) && existingDimMap.containsKey(dim.getCompareModelDimNo())) {
                // 更新场景
                CompareModelDim existingDim = existingDimMap.remove(dim.getCompareModelDimNo());
                dim.setId(existingDim.getId());
                updateList.add(dim);
            } else {
                // 新增场景
                dim.setCompareModelDimNo(IdUtil.objectId());
                saveList.add(dim);
            }
        }

        this.saveBatch(saveList);
        this.updateBatchByIdAndEnterpriseNo(updateList);

        // 处理需要删除的维度
        if (!existingDimMap.isEmpty()) {
            Collection<CompareModelDim> needDeleteDims = existingDimMap.values();
            for (CompareModelDim needDeleteDim : needDeleteDims) {
                needDeleteDim.setDeleted(DeletedEnum.DELETED.getValue());
            }
            this.updateBatchByIdAndEnterpriseNo(needDeleteDims);
        }
    }

    /**
     * 更新比对对象/比对维度
     * @param compareModelSaveReq
     * @param compareModel
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateModelRefDim(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel) {
        List<CompareModelDim> dbModelDimList = this.list(Wrappers.lambdaQuery(CompareModelDim.class)
                .eq(CompareModelDim::getCompareModelNo, compareModel.getModelNo())
                .eq(CompareModelDim::getEnterpriseNo, compareModel.getEnterpriseNo())
                .eq(CompareModelDim::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, CompareModelDim> existingDimMap = dbModelDimList.stream()
                .collect(Collectors.toMap(CompareModelDim::getCompareModelDimNo, Function.identity()));

        this.saveModelRefDim(compareModelSaveReq, compareModel, existingDimMap);
    }


    /**
     * 填充维度信息（比对对象和比对维度）
     * @param compareModelGetRes 返回结果对象
     * @param compareModel 主模型对象
     */
    @Override
    public void fillDimList(CompareModelGetRes compareModelGetRes, CompareModel compareModel) {
        List<CompareModelDim> dimList = this.list(
                Wrappers.lambdaQuery(CompareModelDim.class)
                        .eq(CompareModelDim::getCompareModelNo, compareModel.getModelNo())
                        .eq(CompareModelDim::getEnterpriseNo, compareModel.getEnterpriseNo())
                        .eq(CompareModelDim::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .orderByAsc(CompareModelDim::getSort)
        );

        if (CollectionUtils.isNotEmpty(dimList)) {
            // 分离比对对象和比对维度
            List<CompareModelDim> compareObjectList = dimList.stream()
                    .filter(dim -> CompareModelDimTypeEnum.COMPARE_OBJECT.getValue().equals(dim.getDimType()))
                    .collect(Collectors.toList());

            List<CompareModelDim> compareDimList = dimList.stream()
                    .filter(dim -> CompareModelDimTypeEnum.COMPARE_DIM.getValue().equals(dim.getDimType()))
                    .collect(Collectors.toList());

            // 转换并设置
            if (CollectionUtils.isNotEmpty(compareObjectList)) {
                List<CompareModelRefFieldRes> compareObjectResList = BeanCopyUtil.copyFieldsList(compareObjectList, CompareModelRefFieldRes.class);
                compareModelGetRes.setCompareObjectList(compareObjectResList);
            }

            if (CollectionUtils.isNotEmpty(compareDimList)) {
                List<CompareModelRefFieldRes> compareDimResList = BeanCopyUtil.copyFieldsList(compareDimList, CompareModelRefFieldRes.class);
                compareModelGetRes.setCompareDimList(compareDimResList);
            }
        }
    }


    @Override
    public List<CompareModelRefFieldRes> getModelDimList(String enterpriseNo, String modelNo) {
        List<CompareModelDim> dimList = this.list(
                Wrappers.lambdaQuery(CompareModelDim.class)
                        .eq(CompareModelDim::getCompareModelNo, modelNo)
                        .eq(CompareModelDim::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .orderByAsc(CompareModelDim::getSort)
        );

        if (CollectionUtils.isNotEmpty(dimList)) {
            return BeanCopyUtil.copyFieldsList(dimList, CompareModelRefFieldRes.class);
        }
        return ListUtil.empty();
    }

    @Override
    public void checkModelDimExist(ComparePlanSaveReq comparePlanSaveReq) {
        List<CompareModelDim> dimList = this.list(
                Wrappers.lambdaQuery(CompareModelDim.class)
                        .eq(CompareModelDim::getCompareModelNo, comparePlanSaveReq.getCompareModelNo())
                        .eq(CompareModelDim::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .orderByAsc(CompareModelDim::getSort)
        );
        Map<String, CompareModelDim> compareModelDimMap = dimList.stream().collect(Collectors.toMap(CompareModelDim::getCompareModelDimNo, Function.identity()));

        checkModelDim(comparePlanSaveReq.getRowList(), compareModelDimMap);
        checkModelDim(comparePlanSaveReq.getColumnList(), compareModelDimMap);
        checkModelDim(comparePlanSaveReq.getValueList(), compareModelDimMap);

    }

    /**
     * 真正校验比价方案报表引用模型字段是否存在
     * @param fieldList
     * @param compareModelDimMap
     */
    private void checkModelDim(List<ComparePlanRefFieldReq> fieldList, Map<String, CompareModelDim> compareModelDimMap) {
        if (CollectionUtils.isNotEmpty(fieldList)) {
            for (ComparePlanRefFieldReq field : fieldList) {
                if (Objects.equals(ComparePlanFieldSourceTypeEnum.COMPARE_OBJECT.getValue(), field.getSourceType())) {
                    CompareModelDim compareModelDim = compareModelDimMap.get(field.getCompareModelDimNo());
                    if (compareModelDim == null) {
                        throw new BusinessException("模型字段编号【"+ field.getCompareModelDimNo()+"】不存在");
                    }
                    if (!Objects.equals(compareModelDim.getDimType(), CompareModelDimTypeEnum.COMPARE_OBJECT.getValue())) {
                        throw new BusinessException("模型字段编号【"+ field.getCompareModelDimNo()+"】不是比对对象");
                    }
                } else if (Objects.equals(ComparePlanFieldSourceTypeEnum.COMPARE_DIM.getValue(), field.getSourceType())) {
                    CompareModelDim compareModelDim = compareModelDimMap.get(field.getCompareModelDimNo());
                    if (compareModelDim == null) {
                        throw new BusinessException("模型字段编号【"+ field.getCompareModelDimNo()+"】不存在");
                    }
                    if (!Objects.equals(compareModelDim.getDimType(), CompareModelDimTypeEnum.COMPARE_DIM.getValue())) {
                        throw new BusinessException("模型字段编号【"+ field.getCompareModelDimNo()+"】不是比对维度");
                    }
                }
            }
        }

    }


}
