package com.yyigou.ddc.dmp.web.vo.subject;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 分析主题表实体类
 */
@Data
public class AnalysisSubjectVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分析主题唯一标识
     */
    @EntityField(name = "分析主题唯一标识")
    private String subjectNo;

}