package com.yyigou.ddc.dmp.web.dto.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class DatasetPreviewDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据集唯一标识
     */
    @EntityField(name = "数据集唯一标识")
    private String datasetNo;

    /**
     * 预览数据条数，默认10条
     */
    @EntityField(name = "预览数据条数，默认10条")
    private Integer limit = 10;
}
