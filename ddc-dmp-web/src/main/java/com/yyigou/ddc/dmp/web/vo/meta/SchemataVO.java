package com.yyigou.ddc.dmp.web.vo.meta;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SchemataVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "目录名称")
    private String catalogName;

    @EntityField(name = "模式名称")
    private String schemaName;

    @EntityField(name = "默认字符集名称")
    private String defaultCharacterSetName;

    @EntityField(name = "默认排序规则名称")
    private String defaultCollationName;

    @EntityField(name = "SQL路径")
    private String sqlPath;

    @EntityField(name = "默认加密")
    private String defaultEncryption;
}
