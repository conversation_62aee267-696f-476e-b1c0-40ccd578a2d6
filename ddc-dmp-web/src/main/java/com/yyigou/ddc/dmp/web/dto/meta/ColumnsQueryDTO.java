package com.yyigou.ddc.dmp.web.dto.meta;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ColumnsQueryDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "模式名称")
    private String schemaName;

    @EntityField(name = "表名称")
    private String tableName;

}
