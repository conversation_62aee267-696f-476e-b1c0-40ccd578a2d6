package com.yyigou.ddc.dmp.web.vo.meta;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TablesVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "表目录")
    private String tableCatalog;

    @EntityField(name = "表模式")
    private String tableSchema;

    @EntityField(name = "表名称")
    private String tableName;

    @EntityField(name = "表类型")
    private String tableType;

    @EntityField(name = "存储引擎")
    private String engine;

    @EntityField(name = "版本")
    private Long version;

    @EntityField(name = "行格式")
    private String rowFormat;

    @EntityField(name = "表行数")
    private Long tableRows;

    @EntityField(name = "平均行长度")
    private Long avgRowLength;

    @EntityField(name = "数据长度")
    private Long dataLength;

    @EntityField(name = "最大数据长度")
    private Long maxDataLength;

    @EntityField(name = "索引长度")
    private Long indexLength;

    @EntityField(name = "空闲数据")
    private Long dataFree;

    @EntityField(name = "自增值")
    private Long autoIncrement;

    @EntityField(name = "创建时间")
    private LocalDateTime createTime;

    @EntityField(name = "更新时间")
    private LocalDateTime updateTime;

    @EntityField(name = "检查时间")
    private LocalDateTime checkTime;
    @EntityField(name = "表排序规则")
    private String tableCollation;

    @EntityField(name = "校验和")
    private Long checksum;

    @EntityField(name = "创建选项")
    private String createOptions;

    @EntityField(name = "表注释")
    private String tableComment;
}
