package com.yyigou.ddc.dmp.service.dimension;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.dao.dimension.entity.Dimension;
import com.yyigou.ddc.dmp.model.req.dimension.*;
import com.yyigou.ddc.dmp.model.res.dimension.DatasetFieldDimensionRefRes;
import com.yyigou.ddc.dmp.model.res.dimension.DimensionRes;

import java.util.List;

public interface DimensionService extends IService<Dimension> {
    /**
     * 保存维度
     *
     * @param dimensionSaveReq
     * @return
     */
    String saveDimension(DimensionSaveReq dimensionSaveReq);

    /**
     * 删除维度
     *
     * @param dimensionDeleteReq
     */
    void deleteDimension(DimensionDeleteReq dimensionDeleteReq);

    /**
     * 启停维度
     *
     * @param dimensionStartStopReq
     */
    void startStop(DimensionStartStopReq dimensionStartStopReq);

    /**
     * 查看维度
     *
     * @param dimensionGetReq
     * @return
     */
    DimensionRes getDimension(DimensionGetReq dimensionGetReq);

    /**
     * 查看维度列表
     *
     * @param dimensionGetListReq
     * @return
     */
    List<DimensionRes> getDimensionList(DimensionGetListReq dimensionGetListReq);

    /**
     * 查询维度
     *
     * @param dimensionPageReq
     * @return
     */
    PageVo<DimensionRes> queryListPage(DimensionPageReq dimensionPageReq, PageDto pageDTO);

    /**
     * 查询数据集字段和维度关联关系
     *
     * @param datasetFieldDimensionRefQueryReq
     * @return
     */
    List<DatasetFieldDimensionRefRes> queryDatasetFieldDimensionRef(DatasetFieldDimensionRefQueryReq datasetFieldDimensionRefQueryReq);

    /**
     * 关联数据集字段和维度
     *
     * @param datasetFieldDimensionRefSaveReq
     * @return
     */
    Long saveDatasetFieldDimensionRef(DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq);

}