package com.yyigou.ddc.dmp.web.vo.metric;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MetricVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String enterpriseNo;
    private String metricNo;
    private String metricCode;
    private String metricName;
    private String unit;
    private String numPrecision;
    private String displayFormat;
    private String description;
    private String datasetNo;
    private String datasetFieldNo;
    private String aggType;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}

