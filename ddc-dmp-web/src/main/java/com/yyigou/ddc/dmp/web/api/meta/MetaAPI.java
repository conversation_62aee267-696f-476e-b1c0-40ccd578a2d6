package com.yyigou.ddc.dmp.web.api.meta;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.meta.ColumnsQueryDTO;
import com.yyigou.ddc.dmp.web.dto.meta.TablesQueryDTO;
import com.yyigou.ddc.dmp.web.vo.meta.ColumnsVO;
import com.yyigou.ddc.dmp.web.vo.meta.SchemataVO;
import com.yyigou.ddc.dmp.web.vo.meta.TablesVO;

import java.util.List;

public interface MetaAPI extends ServiceBase {
    CallResult<List<SchemataVO>> querySchemas();

    CallResult<List<TablesVO>> queryTables(TablesQueryDTO params);

    CallResult<List<ColumnsVO>> queryColumns(ColumnsQueryDTO params);
}
