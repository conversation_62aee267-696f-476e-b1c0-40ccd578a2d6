package com.yyigou.ddc.dmp.web.api.dimension;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionDeleteReq;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionStartStopReq;
import com.yyigou.ddc.dmp.web.dto.dimension.*;
import com.yyigou.ddc.dmp.web.vo.dimension.DatasetFieldDimensionRefVO;
import com.yyigou.ddc.dmp.web.vo.dimension.DimensionVO;

public interface DimensionAPI extends ServiceBase {
    CallResult<PageVo<DimensionVO>> findListPage(DimensionPageDTO params, PageDto pageParams);

    CallResult<DimensionVO> saveDimension(DimensionSaveDTO params);

    CallResult<DimensionVO> getDimension(DimensionGetDTO params);

    CallResult<Boolean> deleteDimension(DimensionDeleteDTO params);

    CallResult<Boolean> startStop(DimensionStartStopDTO params);

    CallResult<DatasetFieldDimensionRefVO> saveDatasetFieldDimensionRef(DatasetFieldDimensionRefSaveDTO params);
}