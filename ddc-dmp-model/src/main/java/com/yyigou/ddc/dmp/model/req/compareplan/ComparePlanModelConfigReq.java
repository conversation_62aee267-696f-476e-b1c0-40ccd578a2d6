package com.yyigou.ddc.dmp.model.req.compareplan;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanModelConfigReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 比对模型编号
     */
    @NotBlank(message = "比对模型不能为空")
    private String compareModelNo;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空")
    private String enterpriseNo;
}
