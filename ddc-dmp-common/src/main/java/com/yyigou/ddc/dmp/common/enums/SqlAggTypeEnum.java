package com.yyigou.ddc.dmp.common.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum SqlAggTypeEnum {

    SUM("SUM",1),
    MIN("MIN",2),
    MAX("MAX",3),
    COUNT("COUNT",4),
    ;

    private final String aggType;
    private final Integer aggTypeCode;

    SqlAggTypeEnum(String aggType, Integer joinTypeCode) {
        this.aggType = aggType;
        this.aggTypeCode = joinTypeCode;
    }


    /**
     * 根据字符串值获取对应的 JOIN 枚举
     */
    public static SqlAggTypeEnum getByJoinType(String type) {
        for (SqlAggTypeEnum value : values()) {
            if (value.getAggType().equalsIgnoreCase(type)) {
                return value;
            }
        }
        return null;
    }

    public static SqlAggTypeEnum getByJoinTypeCode(Integer joinTypeCode) {
        for (SqlAggTypeEnum value : values()) {
            if (value.getAggTypeCode().equals(joinTypeCode)) {
                return value;
            }
        }
        return null;
    }

}
