package com.yyigou.ddc.dmp.web.provider.metric;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.req.metric.MetricGetReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricPageReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricSaveReq;
import com.yyigou.ddc.dmp.model.res.metric.MetricRes;
import com.yyigou.ddc.dmp.service.metric.MetricService;
import com.yyigou.ddc.dmp.web.api.metric.MetricAPI;
import com.yyigou.ddc.dmp.web.dto.metric.MetricGetDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricPageDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricSaveDTO;
import com.yyigou.ddc.dmp.web.vo.metric.MetricVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Interface(name = "指标")
@Component("metricProvider")
public class MetricProvider extends ServiceBaseAbstract implements MetricAPI {
    @Resource
    private MetricService metricService;

    @Override
    @Method(aliasName = "ddc.dmp.metric.save", name = "指标保存", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<MetricVO> save(MetricSaveDTO params) {
        try {
            MetricSaveReq metricSaveReq = BeanCopyUtil.copyFieldsByJson(params, MetricSaveReq.class);
//        metricSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            metricSaveReq.setEnterpriseNo("2000002");
            String metricNo = metricService.saveMetric(metricSaveReq);

            MetricVO metricVO = new MetricVO();
            metricVO.setMetricNo(metricNo);
            return CallResult.success(metricVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.metric.queryListPage", name = "指标查询", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<PageVo<MetricVO>> queryListPage(MetricPageDTO params, PageDto pageParams) {
        try {
            MetricPageReq metricPageReq = BeanCopyUtil.copyFieldsByJson(params, MetricPageReq.class);
//        metricQueryReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            metricPageReq.setEnterpriseNo("2000002");
            PageVo<MetricRes> metricResPageVo = metricService.queryListPage(metricPageReq, pageParams);

            List<MetricVO> metricVOList = BeanCopyUtil.copyFieldsList(metricResPageVo.getRows(), MetricVO.class);
            PageVo<MetricVO> pageVo = new PageVo<>(metricResPageVo.getPageIndex(), metricResPageVo.getPageSize(), metricResPageVo.getTotal(), metricVOList);
            return CallResult.success(pageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.metric.get", name = "指标查看", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<MetricVO> get(MetricGetDTO params) {
        try {
            MetricGetReq metricGetReq = BeanCopyUtil.copyFieldsByJson(params, MetricGetReq.class);
//        metricGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            metricGetReq.setEnterpriseNo("2000002");
            MetricRes metricRes = metricService.getMetric(metricGetReq);

            MetricVO metricVO = BeanCopyUtil.copyFields(metricRes, MetricVO.class);
            return CallResult.success(metricVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
