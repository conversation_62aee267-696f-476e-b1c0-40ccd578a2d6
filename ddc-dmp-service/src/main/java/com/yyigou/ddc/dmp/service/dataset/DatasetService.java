package com.yyigou.ddc.dmp.service.dataset;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetValidateRes;

import java.util.List;

public interface DatasetService extends IService<Dataset> {
    /**
     * 保存数据集
     * @param datasetSaveReq
     * @return
     */
    String saveDataset(DatasetSaveReq datasetSaveReq);

    /**
     * 删除数据集
     * @param datasetDeleteReq
     */
    void deleteDataset(DatasetDeleteReq datasetDeleteReq);

    /**
     * 启停数据集
     * @param datasetStartStopReq
     */
    void startStop(DatasetStartStopReq datasetStartStopReq);

    /**
     * 关联数据集字段和维度
     * @param datasetFieldRefDimensionSaveReq
     * @return
     */
    Long refDimension(DatasetFieldRefDimensionSaveReq datasetFieldRefDimensionSaveReq);

    /**
     * 查询数据集
     * @param datasetPageReq
     * @return
     */
    PageVo<DatasetDetailRes> queryListPage(DatasetPageReq datasetPageReq, PageDto pageDto);

    /**
     * 查看数据集
     * @param datasetGetReq
     * @return
     */
    DatasetDetailRes getDataset(DatasetGetReq datasetGetReq);

    /**
     * 查看数据集列表
     * @param datasetGetListReq
     * @return
     */
    List<DatasetDetailRes> getDatasetList(DatasetGetListReq datasetGetListReq);

    /**
     * 预览数据集数据
     *
     * @param datasetPreviewReq 预览请求参数
     * @return 预览数据结果
     */
    DatasetPreviewRes previewData(DatasetPreviewReq datasetPreviewReq);

    /**
     * 验证数据集配置
     *
     * @param datasetSaveReq 数据集配置请求参数
     * @return 验证结果
     */
    DatasetValidateRes validateDataset(DatasetSaveReq datasetSaveReq);
}