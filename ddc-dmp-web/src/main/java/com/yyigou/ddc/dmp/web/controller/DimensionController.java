package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.model.req.dimension.DatasetFieldDimensionRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionPageReq;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionSaveReq;
import com.yyigou.ddc.dmp.model.res.dimension.DimensionRes;
import com.yyigou.ddc.dmp.service.dimension.DimensionService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.dimension.DatasetFieldDimensionRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dimension.DimensionPageDTO;
import com.yyigou.ddc.dmp.web.dto.dimension.DimensionSaveDTO;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.dimension.DatasetFieldDimensionRefVO;
import com.yyigou.ddc.dmp.web.vo.dimension.DimensionVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 维度前端控制器
 */
@RestController
@RequestMapping("/dimension")
public class DimensionController {
    @Resource
    private DimensionService dimensionService;


    /**
     * 查询数据集
     */
    @GetMapping("/queryListPage")
    public PageVo<DimensionVO> queryListPage(@RequestBody CommonDTO<DimensionPageDTO> params) {
        DimensionPageReq dimensionPageReq = BeanCopyUtil.copyFieldsByJson(params, DimensionPageReq.class);
//        dimensionQueryReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        dimensionPageReq.setEnterpriseNo("2000002");
        PageDto pageDTO = new PageDto();
        PageVo<DimensionRes> dimensionResPageVo = dimensionService.queryListPage(dimensionPageReq, pageDTO);

        List<DimensionVO> dimensionVOS = BeanCopyUtil.copyFieldsList(dimensionResPageVo.getRows(), DimensionVO.class);
        PageVo<DimensionVO> pageVo = new PageVo<>(dimensionResPageVo.getPageIndex(), dimensionResPageVo.getPageSize(), dimensionResPageVo.getTotal(), dimensionVOS);
        return pageVo;
    }

    /**
     * 保存维度
     */
    @PostMapping("/save")
    public DimensionVO saveDimension(@RequestBody CommonDTO<DimensionSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        DimensionSaveReq dimensionSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), DimensionSaveReq.class);
//        dimensionSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        dimensionSaveReq.setEnterpriseNo("2000002");
        String dimensionNo = dimensionService.saveDimension(dimensionSaveReq);

        DimensionVO dimensionVO = new DimensionVO();
        dimensionVO.setDimensionNo(dimensionNo);
        return dimensionVO;
    }


    /**
     * 绑定数据集字段和维度
     */
    @PostMapping("/saveDatasetFieldDimensionRef")
    public DatasetFieldDimensionRefVO saveDatasetFieldDimensionRef(@RequestBody CommonDTO<DatasetFieldDimensionRefSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), DatasetFieldDimensionRefSaveReq.class);
//        datasetFieldDimensionRefSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetFieldDimensionRefSaveReq.setEnterpriseNo("2000002");
        Long refId = dimensionService.saveDatasetFieldDimensionRef(datasetFieldDimensionRefSaveReq);

        DatasetFieldDimensionRefVO datasetFieldDimensionRefVO = new DatasetFieldDimensionRefVO();
        datasetFieldDimensionRefVO.setId(refId);
        return datasetFieldDimensionRefVO;
    }
}