package com.yyigou.ddc.dmp.web.vo.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class DatasetValidateVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据集唯一标识
     */
    @EntityField(name = "数据集唯一标识")
    private String datasetNo;

    /**
     * 校验是否通过
     */
    @EntityField(name = "校验是否通过")
    private Boolean valid;

    @EntityField(name = "预览SQL")
    private String sql;

    @EntityField(name = "校验失败信息")
    private String message;
}
