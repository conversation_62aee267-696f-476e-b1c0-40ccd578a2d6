package com.yyigou.ddc.dmp.service.meta;

import com.yyigou.ddc.dmp.model.res.meta.ColumnsRes;
import com.yyigou.ddc.dmp.model.res.meta.SchemataRes;
import com.yyigou.ddc.dmp.model.res.meta.TablesRes;

import java.util.List;

public interface MetaService {
    List<SchemataRes> loadSchematas();

    SchemataRes getSchemata(String schemaName);

    List<TablesRes> loadTables(String schemaName);

    TablesRes getTable(String schemaName, String tableName);

    List<ColumnsRes> loadColumns(String schemaName, String tableName);

    List<ColumnsRes> loadColumnsByTables(String schemaName, List<String> tableNameList);

    ColumnsRes getColumn(String schemaName, String tableName, String columnName);

}
