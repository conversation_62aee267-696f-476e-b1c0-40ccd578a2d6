package com.yyigou.ddc.dmp.web.dto.dataset;

import lombok.Data;

import java.io.Serial;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
public class DatasetJoinRelSaveDTO {
    @Serial
    private static final long serialVersionUID = 1L;

    private String enterpriseNo;
    private String datasetNo;
    private String targetCatalogName;
    private String targetSchemaName;
    private String targetTableName;
    private String joinType;
    private String joinCondition;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}

