package com.yyigou.ddc.dmp.web.dto.dimension;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class DimensionStartStopDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "维度编号")
    private String dimensionNo;

    @EntityField(name = "状态")
    private Integer status;
}
