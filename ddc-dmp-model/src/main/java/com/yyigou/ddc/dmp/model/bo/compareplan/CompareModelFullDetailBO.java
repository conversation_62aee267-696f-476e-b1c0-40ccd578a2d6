package com.yyigou.ddc.dmp.model.bo.compareplan;

import com.yyigou.ddc.dmp.model.bo.comparemodel.ModelDatasetBO;
import com.yyigou.ddc.dmp.model.bo.comparemodel.ModelMetricBO;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelFullDetailBO extends CompareModelGetRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据集信息
     * key 是 datasetNo
     */
    private Map<String, ModelDatasetBO> datasetMap;


    /**
     * 指标信息
     * key 是 compareModelMetricNo
     */
    private Map<String, ModelMetricBO> metricMap;

}
