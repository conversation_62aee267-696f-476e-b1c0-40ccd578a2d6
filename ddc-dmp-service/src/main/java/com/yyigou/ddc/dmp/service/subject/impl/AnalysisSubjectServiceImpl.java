package com.yyigou.ddc.dmp.service.subject.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.StatusEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.AnalysisSubjectDatasetRef;
import com.yyigou.ddc.dmp.dao.dataset.mapper.AnalysisSubjectDatasetRefMapper;
import com.yyigou.ddc.dmp.dao.subject.entity.AnalysisSubject;
import com.yyigou.ddc.dmp.dao.subject.mapper.AnalysisSubjectMapper;
import com.yyigou.ddc.dmp.model.req.subject.*;
import com.yyigou.ddc.dmp.model.res.subject.SubjectRes;
import com.yyigou.ddc.dmp.service.subject.AnalysisSubjectService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AnalysisSubjectServiceImpl extends ServiceImpl<AnalysisSubjectMapper, AnalysisSubject> implements AnalysisSubjectService {
    @Resource
    private AnalysisSubjectDatasetRefMapper analysisSubjectDatasetRefMapper;


    @Override
    @Transactional
    public String saveSubject(SubjectSaveReq subjectSaveReq) {
        AnalysisSubject analysisSubject = BeanCopyUtil.copyFieldsByJson(subjectSaveReq, AnalysisSubject.class);
        if (StringUtils.isEmpty(analysisSubject.getSubjectNo())) {
            //新增
            analysisSubject.setSubjectNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<AnalysisSubject>lambdaQuery()
                    .eq(AnalysisSubject::getEnterpriseNo, analysisSubject.getEnterpriseNo())
                    .eq(AnalysisSubject::getSubjectNo, analysisSubject.getSubjectNo())
                    .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("分析主题不存在");
            }

            AnalysisSubject toDeleteAnalysisSubject = new AnalysisSubject();
            toDeleteAnalysisSubject.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteAnalysisSubject, Wrappers.<AnalysisSubject>lambdaQuery()
                    .eq(AnalysisSubject::getEnterpriseNo, analysisSubject.getEnterpriseNo())
                    .eq(AnalysisSubject::getSubjectNo, analysisSubject.getSubjectNo())
                    .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(analysisSubject);

        return analysisSubject.getSubjectNo();
    }

    @Override
    public void deleteSubject(SubjectDeleteReq subjectDeleteReq) {
        //TODO shenbin 依赖校验
        AnalysisSubject toDeleteSubject = new AnalysisSubject();
        toDeleteSubject.setDeleted(DeletedEnum.DELETED.getValue());
        update(toDeleteSubject, Wrappers.<AnalysisSubject>lambdaQuery()
                .eq(AnalysisSubject::getEnterpriseNo, subjectDeleteReq.getEnterpriseNo())
                .eq(AnalysisSubject::getSubjectNo, subjectDeleteReq.getSubjectNo())
                .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
    }

    @Override
    public void startStop(SubjectStartStopReq subjectStartStopReq) {
        ValidatorUtil.checkEmptyThrowEx(subjectStartStopReq.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtil.checkEmptyThrowEx(subjectStartStopReq.getSubjectNo(), "分析主题编号不能为空");
        ValidatorUtil.checkEmptyThrowEx(subjectStartStopReq.getStatus(), "启停状态不能为空");
        StatusEnum status = StatusEnum.getByValue(subjectStartStopReq.getStatus());
        ValidatorUtil.checkTrueThrowEx(status == null, "启停状态不正确");

        AnalysisSubject toUpdateSubject = new AnalysisSubject();
        toUpdateSubject.setStatus(subjectStartStopReq.getStatus());
        update(toUpdateSubject, Wrappers.<AnalysisSubject>lambdaQuery()
                .eq(AnalysisSubject::getEnterpriseNo, subjectStartStopReq.getEnterpriseNo())
                .eq(AnalysisSubject::getSubjectNo, subjectStartStopReq.getSubjectNo())
                .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
    }

    @Override
    public SubjectRes getSubject(SubjectGetReq subjectGetReq) {
        AnalysisSubject analysisSubject = getOne(Wrappers.<AnalysisSubject>lambdaQuery()
                .eq(AnalysisSubject::getEnterpriseNo, subjectGetReq.getEnterpriseNo())
                .eq(AnalysisSubject::getSubjectNo, subjectGetReq.getSubjectNo())
                .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == analysisSubject) {
            throw new BusinessException("分析主题不存在");
        }

        SubjectRes subjectRes = BeanCopyUtil.copyFields(analysisSubject, SubjectRes.class);

        List<AnalysisSubjectDatasetRef> analysisSubjectDatasetRefList = analysisSubjectDatasetRefMapper.selectList(Wrappers.<AnalysisSubjectDatasetRef>lambdaQuery()
                .eq(AnalysisSubjectDatasetRef::getEnterpriseNo, analysisSubject.getEnterpriseNo())
                .eq(AnalysisSubjectDatasetRef::getSubjectNo, analysisSubject.getSubjectNo())
                .eq(AnalysisSubjectDatasetRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        // 补充额外信息
        if (CollectionUtils.isNotEmpty(analysisSubjectDatasetRefList)) {
            List<String> datasetNoList = analysisSubjectDatasetRefList.stream().map(AnalysisSubjectDatasetRef::getDatasetNo).collect(Collectors.toList());
            subjectRes.setDatasetNoList(datasetNoList);
        }

        subjectRes.setStatusName(StatusEnum.getByValue(analysisSubject.getStatus()).getName());

        return subjectRes;
    }

    @Override
    @Transactional
    public Long saveSubjectDatasetRef(SubjectDatasetRefSaveReq subjectDatasetRefSaveReq) {
        analysisSubjectDatasetRefMapper.update(null, Wrappers.<AnalysisSubjectDatasetRef>lambdaUpdate()
                .set(AnalysisSubjectDatasetRef::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(AnalysisSubjectDatasetRef::getEnterpriseNo, subjectDatasetRefSaveReq.getEnterpriseNo())
                .eq(AnalysisSubjectDatasetRef::getSubjectNo, subjectDatasetRefSaveReq.getSubjectNo())
                .eq(AnalysisSubjectDatasetRef::getDatasetNo, subjectDatasetRefSaveReq.getDatasetNo())
                .eq(AnalysisSubjectDatasetRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        AnalysisSubjectDatasetRef subjectDataset = new AnalysisSubjectDatasetRef();
        subjectDataset.setEnterpriseNo(subjectDatasetRefSaveReq.getEnterpriseNo());
        subjectDataset.setSubjectNo(subjectDatasetRefSaveReq.getSubjectNo());
        subjectDataset.setDatasetNo(subjectDatasetRefSaveReq.getDatasetNo());
        analysisSubjectDatasetRefMapper.insert(subjectDataset);

        return subjectDataset.getId();
    }
}