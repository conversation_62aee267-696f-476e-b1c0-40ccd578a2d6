package com.yyigou.ddc.dmp.model.req.dimension;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
public class DatasetFieldDimensionRefSaveReq implements Serializable {
    private String enterpriseNo;
    private String datasetNo;
    private String datasetFieldNo;
    private String dimensionNo;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}
