package com.yyigou.ddc.dmp.web.api.metric;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.metric.MetricGetDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricPageDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricSaveDTO;
import com.yyigou.ddc.dmp.web.vo.metric.MetricVO;

import java.util.List;

public interface MetricAPI extends ServiceBase {
    CallResult<MetricVO> save(MetricSaveDTO params);

    CallResult<PageVo<MetricVO>> queryListPage(MetricPageDTO params, PageDto pageParams);

    CallResult<MetricVO> get(MetricGetDTO params);
}
