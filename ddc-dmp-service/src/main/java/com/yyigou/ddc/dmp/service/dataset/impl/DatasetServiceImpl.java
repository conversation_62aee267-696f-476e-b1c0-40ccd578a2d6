package com.yyigou.ddc.dmp.service.dataset.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.StatusEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetField;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetJoinRel;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetFieldMapper;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetJoinRelMapper;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetMapper;
import com.yyigou.ddc.dmp.dao.meta.mapper.MetaExtractor;
import com.yyigou.ddc.dmp.dao.metric.mapper.MetricMapper;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.req.dimension.DatasetFieldDimensionRefQueryReq;
import com.yyigou.ddc.dmp.model.req.dimension.DatasetFieldDimensionRefSaveReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricGetListByDatasetNoReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricGetListReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricPageReq;
import com.yyigou.ddc.dmp.model.req.subject.SubjectDatasetRefSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.*;
import com.yyigou.ddc.dmp.model.res.dimension.DatasetFieldDimensionRefRes;
import com.yyigou.ddc.dmp.model.res.metric.MetricRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.service.dimension.DimensionService;
import com.yyigou.ddc.dmp.service.metric.MetricService;
import com.yyigou.ddc.dmp.service.metric.impl.MetricServiceImpl;
import com.yyigou.ddc.dmp.service.subject.AnalysisSubjectService;
import com.yyigou.ddc.dmp.service.util.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.NestedExceptionUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DatasetServiceImpl extends ServiceImpl<DatasetMapper, Dataset> implements DatasetService {
    @Resource
    private DatasetMapper datasetMapper;

    @Resource
    private DatasetJoinRelMapper datasetJoinRelMapper;

    @Resource
    private DatasetFieldMapper datasetFieldMapper;

    @Resource
    private AnalysisSubjectService analysisSubjectService;

    @Resource
    private DimensionService dimensionService;

    @Resource
    private MetricService metricService;

    @Resource
    private MetaExtractor metaExtractor;

    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;
    @Autowired
    private MetricMapper metricMapper;
    @Autowired
    private MetricServiceImpl metricServiceImpl;

    @Override
    @Transactional
    public String saveDataset(DatasetSaveReq datasetSaveReq) {
        DatasetValidateRes datasetValidateRes = validateDataset(datasetSaveReq);
        if (!datasetValidateRes.getValid()) {
            throw new BusinessException(datasetValidateRes.getMessage());
        }

        Dataset dataset = BeanCopyUtil.copyFields(datasetSaveReq, Dataset.class);
        if (StringUtils.isEmpty(dataset.getDatasetNo())) {
            //新增
            dataset.setDatasetNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<Dataset>lambdaQuery()
                    .eq(Dataset::getEnterpriseNo, dataset.getEnterpriseNo())
                    .eq(Dataset::getDatasetNo, dataset.getDatasetNo())
                    .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("数据集不存在");
            }

            Dataset toDeleteDataset = new Dataset();
            toDeleteDataset.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteDataset, Wrappers.<Dataset>lambdaQuery()
                    .eq(Dataset::getEnterpriseNo, dataset.getEnterpriseNo())
                    .eq(Dataset::getDatasetNo, dataset.getDatasetNo())
                    .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(dataset);

        SubjectDatasetRefSaveReq subjectDatasetRefSaveReq = new SubjectDatasetRefSaveReq();
        subjectDatasetRefSaveReq.setEnterpriseNo(dataset.getEnterpriseNo());
        subjectDatasetRefSaveReq.setSubjectNo(datasetSaveReq.getSubjectNo());
        subjectDatasetRefSaveReq.setDatasetNo(dataset.getDatasetNo());
        analysisSubjectService.saveSubjectDatasetRef(subjectDatasetRefSaveReq);

        List<DatasetJoinRel> datasetJoinRels = BeanCopyUtil.copyFieldsList(datasetSaveReq.getDatasetJoinRels(), DatasetJoinRel.class);
        replaceDatasetJoinRels(dataset, datasetJoinRels);

        List<DatasetField> datasetFields = BeanCopyUtil.copyFieldsList(datasetSaveReq.getDatasetFields(), DatasetField.class);
        replaceDatesetFields(dataset, datasetFields);

        return dataset.getDatasetNo();
    }

    @Override
    @Transactional
    public void deleteDataset(DatasetDeleteReq datasetDeleteReq) {
        ValidatorUtil.checkEmptyThrowEx(datasetDeleteReq.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetDeleteReq.getDatasetNo(), "数据集编号不能为空");

        //TODO shenbin 依赖项校验

        Dataset toDeleteDataset = new Dataset();
        toDeleteDataset.setDeleted(DeletedEnum.DELETED.getValue());
        update(toDeleteDataset, Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, datasetDeleteReq.getEnterpriseNo())
                .eq(Dataset::getDatasetNo, datasetDeleteReq.getDatasetNo())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        DatasetJoinRel toDeleteDatasetJoinRel = new DatasetJoinRel();
        toDeleteDatasetJoinRel.setDeleted(DeletedEnum.DELETED.getValue());
        datasetJoinRelMapper.update(toDeleteDatasetJoinRel, Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, datasetDeleteReq.getEnterpriseNo())
                .eq(DatasetJoinRel::getDatasetNo, datasetDeleteReq.getDatasetNo())
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        DatasetField toDeleteDatasetField = new DatasetField();
        toDeleteDatasetField.setDeleted(DeletedEnum.DELETED.getValue());
        datasetFieldMapper.update(toDeleteDatasetField, Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, datasetDeleteReq.getEnterpriseNo())
                .eq(DatasetField::getDatasetNo, datasetDeleteReq.getDatasetNo())
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
    }

    @Override
    public void startStop(DatasetStartStopReq datasetStartStopReq) {
        ValidatorUtil.checkEmptyThrowEx(datasetStartStopReq.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetStartStopReq.getDatasetNo(), "数据集编号不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetStartStopReq.getStatus(), "启停状态不能为空");
        StatusEnum status = StatusEnum.getByValue(datasetStartStopReq.getStatus());
        ValidatorUtil.checkTrueThrowEx(status == null, "启停状态不正确");

        Dataset toUpdateDataset = new Dataset();
        toUpdateDataset.setStatus(status.getValue());

        update(toUpdateDataset, Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, datasetStartStopReq.getEnterpriseNo())
                .eq(Dataset::getDatasetNo, datasetStartStopReq.getDatasetNo())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        DatasetJoinRel toUpdateJoinRel = new DatasetJoinRel();
        toUpdateJoinRel.setStatus(status.getValue());
        datasetJoinRelMapper.update(toUpdateJoinRel, Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, datasetStartStopReq.getEnterpriseNo())
                .eq(DatasetJoinRel::getDatasetNo, datasetStartStopReq.getDatasetNo())
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        DatasetField toUpdateDatasetField = new DatasetField();
        toUpdateDatasetField.setStatus(status.getValue());
        datasetFieldMapper.update(toUpdateDatasetField, Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, datasetStartStopReq.getEnterpriseNo())
                .eq(DatasetField::getDatasetNo, datasetStartStopReq.getDatasetNo())
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
    }

    @Override
    public Long refDimension(DatasetFieldRefDimensionSaveReq datasetFieldRefDimensionSaveReq) {
        DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq = new DatasetFieldDimensionRefSaveReq();
        datasetFieldDimensionRefSaveReq.setEnterpriseNo(datasetFieldRefDimensionSaveReq.getEnterpriseNo());
        datasetFieldDimensionRefSaveReq.setDatasetNo(datasetFieldRefDimensionSaveReq.getDatasetNo());
        datasetFieldDimensionRefSaveReq.setDatasetFieldNo(datasetFieldRefDimensionSaveReq.getDatasetFieldNo());
        datasetFieldDimensionRefSaveReq.setDimensionNo(datasetFieldRefDimensionSaveReq.getDimensionNo());
        datasetFieldDimensionRefSaveReq.setDescription(datasetFieldRefDimensionSaveReq.getDescription());

        return dimensionService.saveDatasetFieldDimensionRef(datasetFieldDimensionRefSaveReq);
    }

    @Override
    public PageVo<DatasetDetailRes> queryListPage(DatasetPageReq datasetPageReq, PageDto pageDto) {
        Page<Dataset> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize(), org.apache.commons.lang3.StringUtils.isBlank(pageDto.getOrderBy()) ? "create_time desc" : pageDto.getOrderBy());

        datasetMapper.selectList(Wrappers.lambdaQuery(Dataset.class)
                .eq(Dataset::getEnterpriseNo, datasetPageReq.getEnterpriseNo())
                .eq(datasetPageReq.getStatus() != null, Dataset::getStatus, datasetPageReq.getStatus())
                .notIn(CollectionUtils.isNotEmpty(datasetPageReq.getExcludeDatasetNoList()), Dataset::getDatasetNo, datasetPageReq.getExcludeDatasetNoList())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page,
                data -> wrapDataset(datasetPageReq.getEnterpriseNo(), data)
        );
    }

    private List<DatasetDetailRes> wrapDataset(String enterpriseNo, List<Dataset> datasets) {
        if (CollectionUtils.isEmpty(datasets)) {
            return Collections.emptyList();
        }

        Set<String> datasetNoSet = datasets.stream().map(Dataset::getDatasetNo).collect(Collectors.toSet());

        List<DatasetJoinRel> datasetJoinRels = datasetJoinRelMapper.selectList(Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, enterpriseNo)
                .in(DatasetJoinRel::getDatasetNo, datasetNoSet)
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<DatasetJoinRel>> datasetNoToJoinRelMap = datasetJoinRels.stream().collect(Collectors.groupingBy(DatasetJoinRel::getDatasetNo));

        List<DatasetField> datasetFields = datasetFieldMapper.selectList(Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, enterpriseNo)
                .in(DatasetField::getDatasetNo, datasetNoSet)
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<DatasetField>> datasetNoToFieldMap = datasetFields.stream().collect(Collectors.groupingBy(DatasetField::getDatasetNo));

        List<DatasetDetailRes> datasetDetailResList = BeanCopyUtil.copyFieldsList(datasets, DatasetDetailRes.class);
        for (DatasetDetailRes datasetDetailRes : datasetDetailResList) {
            List<DatasetJoinRel> datasetJoinRelList = datasetNoToJoinRelMap.get(datasetDetailRes.getDatasetNo());
            if (CollectionUtils.isNotEmpty(datasetJoinRelList)) {
                List<DatasetJoinRelDetailRes> datasetJoinRelDetailRes = BeanCopyUtil.copyFieldsList(datasetJoinRelList, DatasetJoinRelDetailRes.class);
                datasetDetailRes.setDatasetJoinRels(datasetJoinRelDetailRes);
            }

            List<DatasetField> datasetFieldList = datasetNoToFieldMap.get(datasetDetailRes.getDatasetNo());
            if (CollectionUtils.isNotEmpty(datasetFieldList)) {
                List<DatasetFieldsDetailRes> datasetFieldsDetailRes = BeanCopyUtil.copyFieldsList(datasetFieldList, DatasetFieldsDetailRes.class);
                datasetDetailRes.setDatasetFields(datasetFieldsDetailRes);

                appendFieldRefInfo(datasetDetailRes.getEnterpriseNo(), datasetDetailRes.getDatasetNo(), datasetDetailRes.getDatasetFields());
            }

            datasetDetailRes.setStatusName(StatusEnum.getByValue(datasetDetailRes.getStatus()).getName());
        }

        return datasetDetailResList;
    }

    private void replaceDatasetJoinRels(Dataset dataset, List<DatasetJoinRel> datasetJoinRels) {
        DatasetJoinRel toDeleteDatasetJoinRel = new DatasetJoinRel();
        toDeleteDatasetJoinRel.setDeleted(DeletedEnum.DELETED.getValue());
        datasetJoinRelMapper.update(toDeleteDatasetJoinRel, Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, dataset.getEnterpriseNo())
                .eq(DatasetJoinRel::getDatasetNo, dataset.getDatasetNo())
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        for (DatasetJoinRel datasetJoinRel : datasetJoinRels) {
            datasetJoinRel.setEnterpriseNo(dataset.getEnterpriseNo());
            datasetJoinRel.setDatasetNo(dataset.getDatasetNo());
        }

        datasetJoinRelMapper.insert(datasetJoinRels);
    }

    private void replaceDatesetFields(Dataset dataset, List<DatasetField> datasetFields) {
        DatasetField toDeleteDatasetField = new DatasetField();
        toDeleteDatasetField.setDeleted(DeletedEnum.DELETED.getValue());
        datasetFieldMapper.update(toDeleteDatasetField, Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, dataset.getEnterpriseNo())
                .eq(DatasetField::getDatasetNo, dataset.getDatasetNo())
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        for (DatasetField datasetField : datasetFields) {
            datasetField.setDatasetFieldNo(UUID.fastUUID().toString());
            datasetField.setEnterpriseNo(dataset.getEnterpriseNo());
            datasetField.setDatasetNo(dataset.getDatasetNo());
        }
        datasetFieldMapper.insert(datasetFields);
    }

    @Override
    public DatasetDetailRes getDataset(DatasetGetReq datasetGetReq) {
        Dataset dataset = getOne(Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, datasetGetReq.getEnterpriseNo())
                .eq(Dataset::getDatasetNo, datasetGetReq.getDatasetNo())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataset) {
            throw new BusinessException("数据集不存在");
        }

        List<DatasetDetailRes> datasetDetailRes = wrapDataset(datasetGetReq.getEnterpriseNo(), Collections.singletonList(dataset));

        return datasetDetailRes.get(0);
    }

    @Override
    public List<DatasetDetailRes> getDatasetList(DatasetGetListReq datasetGetListReq) {
        List<Dataset> datasetList = datasetMapper.selectList(Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, datasetGetListReq.getEnterpriseNo())
                .in(Dataset::getDatasetNo, datasetGetListReq.getDatasetNoList())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isEmpty(datasetList)) {
            return Collections.emptyList();
        }

        return wrapDataset(datasetGetListReq.getEnterpriseNo(), datasetList);
    }

    private void appendFieldRefInfo(String enterpriseNo, String datasetNo, List<DatasetFieldsDetailRes> datasetFields) {
        // 指标查询
        MetricGetListByDatasetNoReq metricGetListByDatasetNoReq = new MetricGetListByDatasetNoReq();
        metricGetListByDatasetNoReq.setEnterpriseNo(enterpriseNo);
        metricGetListByDatasetNoReq.setDatasetNo(datasetNo);
        List<MetricRes> metricResList = metricService.getMetricByDatasetNo(metricGetListByDatasetNoReq);
        Map<String, List<MetricRes>> fieldNoToMetricMap = metricResList.stream().collect(Collectors.groupingBy(MetricRes::getDatasetFieldNo));

        // 维度查询
        DatasetFieldDimensionRefQueryReq datasetFieldDimensionRefQueryReq = new DatasetFieldDimensionRefQueryReq();
        datasetFieldDimensionRefQueryReq.setEnterpriseNo(enterpriseNo);
        datasetFieldDimensionRefQueryReq.setDatasetNoList(Collections.singletonList(datasetNo));
        List<DatasetFieldDimensionRefRes> datasetFieldDimensionRefResList = dimensionService.queryDatasetFieldDimensionRef(datasetFieldDimensionRefQueryReq);
        Map<String, List<DatasetFieldDimensionRefRes>> fieldNoToDimensionMap = datasetFieldDimensionRefResList.stream().collect(Collectors.groupingBy(DatasetFieldDimensionRefRes::getDatasetFieldNo));

        datasetFields.forEach(e -> {
            e.setFieldsRefResList(new ArrayList<>());

            List<MetricRes> refMetricRes = fieldNoToMetricMap.get(e.getDatasetFieldNo());
            if (CollectionUtils.isNotEmpty(refMetricRes)) {
                for (MetricRes metricRes : refMetricRes) {
                    DatasetFieldsRefRes datasetFieldsRefRes = new DatasetFieldsRefRes();
                    datasetFieldsRefRes.setFieldRefType("metric");
                    datasetFieldsRefRes.setFieldRefNo(metricRes.getMetricNo());
                    datasetFieldsRefRes.setFieldRefCode(metricRes.getMetricCode());
                    datasetFieldsRefRes.setFieldRefName(metricRes.getMetricName());
                    e.getFieldsRefResList().add(datasetFieldsRefRes);
                }
            }

            List<DatasetFieldDimensionRefRes> refDatasetFieldDimensionRefRes = fieldNoToDimensionMap.get(e.getDatasetFieldNo());
            if (CollectionUtils.isNotEmpty(refDatasetFieldDimensionRefRes)) {
                for (DatasetFieldDimensionRefRes datasetFieldDimensionRefRes : refDatasetFieldDimensionRefRes) {
                    DatasetFieldsRefRes datasetFieldsRefRes = new DatasetFieldsRefRes();
                    datasetFieldsRefRes.setFieldRefType("dimension");
                    datasetFieldsRefRes.setFieldRefNo(datasetFieldDimensionRefRes.getDimensionNo());
                    datasetFieldsRefRes.setFieldRefCode(datasetFieldDimensionRefRes.getDimensionCode());
                    datasetFieldsRefRes.setFieldRefName(datasetFieldDimensionRefRes.getDimensionName());
                    e.getFieldsRefResList().add(datasetFieldsRefRes);
                }
            }
        });
    }

    @Override
    public DatasetPreviewRes previewData(DatasetPreviewReq datasetPreviewReq) {
        DatasetGetReq datasetGetReq = new DatasetGetReq();
        datasetGetReq.setEnterpriseNo(datasetPreviewReq.getEnterpriseNo());
        datasetGetReq.setDatasetNo(datasetPreviewReq.getDatasetNo());
        DatasetDetailRes dataset = getDataset(datasetGetReq);

        // 3. 构建预览SQL
        String previewSql = buildPreviewSql(dataset, 10);

        // 4. 执行查询 - 暂时使用简单的JDBC查询
        List<Map<String, Object>> maps = dorisJdbcTemplate.queryForList(previewSql);

        DatasetPreviewRes datasetPreviewRes = new DatasetPreviewRes();
        datasetPreviewRes.setEnterpriseNo(datasetPreviewReq.getEnterpriseNo());
        datasetPreviewRes.setDatasetNo(datasetPreviewReq.getDatasetNo());
        datasetPreviewRes.setSql(previewSql);
        datasetPreviewRes.setData(maps);

        return datasetPreviewRes;
    }

    @Override
    public DatasetValidateRes validateDataset(DatasetSaveReq datasetSaveReq) {
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq, "数据集配置不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq.getDrivingSchemaName(), "驱动表schema不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq.getDrivingTableName(), "驱动表名称不能为空");
        if (CollectionUtils.isNotEmpty(datasetSaveReq.getDatasetJoinRels())) {
            for (DatasetJoinRelSaveReq datasetJoinRel : datasetSaveReq.getDatasetJoinRels()) {
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getTargetSchemaName(), "关联表schema不能为空");
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getTargetTableName(), "关联表名称不能为空");
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getJoinCondition(), "关联条件不能为空");
            }
        }

        DatasetDetailRes dataset = BeanCopyUtil.copyFieldsByJson(datasetSaveReq, DatasetDetailRes.class);

        String previewSql = buildPreviewSql(dataset, 0);

        try {
            metaExtractor.validateSQL(previewSql);
            DatasetValidateRes datasetValidateRes = new DatasetValidateRes();
            datasetValidateRes.setDatasetNo(datasetSaveReq.getDatasetNo());
            datasetValidateRes.setValid(true);
            datasetValidateRes.setSql(previewSql);
            datasetValidateRes.setMessage("");

            return datasetValidateRes;
        } catch (BusinessException e) {
            Throwable rootCause = NestedExceptionUtils.getRootCause(e);
            if (rootCause == null) {
                rootCause = e;
            }

            String errorMessage = extractCleanErrorMessage(rootCause);

            DatasetValidateRes datasetValidateRes = new DatasetValidateRes();
            datasetValidateRes.setDatasetNo(datasetSaveReq.getDatasetNo());
            datasetValidateRes.setValid(false);
            datasetValidateRes.setSql(previewSql);
            datasetValidateRes.setMessage(String.format("数据集拼接的SQL：%s；校验失败根因：%s", previewSql, errorMessage));

            return datasetValidateRes;
        }
    }

    private String extractCleanErrorMessage(Throwable root) {
        String message = root.getMessage();
        if (message == null) {
            return root.toString(); // fallback
        }

        // 如果 message 含有类似 "异常类名: 错误信息"，提取冒号后的部分
        int colonIndex = message.indexOf(": ");
        if (colonIndex != -1 && colonIndex + 2 < message.length()) {
            return message.substring(colonIndex + 2).trim();
        }

        // 否则直接返回 message
        return message.trim();
    }

    /**
     * 构建预览SQL
     */
    private String buildPreviewSql(DatasetDetailRes dataset, int previewLimit) {
        ValidatorUtil.checkEmptyThrowEx(dataset, "数据集不存在");
        ValidatorUtil.checkEmptyThrowEx(dataset.getDrivingSchemaName(), "驱动表schema不能为空");
        ValidatorUtil.checkEmptyThrowEx(dataset.getDrivingTableName(), "驱动表不能为空");


        StringBuilder sql = new StringBuilder();

        List<DatasetFieldsDetailRes> datasetFieldsDetailRes = dataset.getDatasetFields();
        if (CollectionUtils.isNotEmpty(datasetFieldsDetailRes)) {
            sql.append("SELECT ");
            for (int i = 0; i < datasetFieldsDetailRes.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                String fieldCatalogName = datasetFieldsDetailRes.get(i).getCatalogName();
                if (StringUtils.isNotEmpty(fieldCatalogName)) {
                    fieldCatalogName = fieldCatalogName + ".";
                } else {
                    fieldCatalogName = "";
                }
                sql.append(fieldCatalogName)
                        .append(datasetFieldsDetailRes.get(i).getSchemaName())
                        .append(".")
                        .append(datasetFieldsDetailRes.get(i).getTableName())
                        .append(".")
                        .append(datasetFieldsDetailRes.get(i).getFieldCode());
            }
        }

        // 基于驱动表构建基础查询
        String drivingCatalogName = dataset.getDrivingCatalogName();
        if (StringUtils.isNotEmpty(drivingCatalogName)) {
            drivingCatalogName = drivingCatalogName + ".";
        } else {
            drivingCatalogName = "";
        }
        sql.append(" FROM ")
                .append(drivingCatalogName)
                .append(dataset.getDrivingSchemaName())
                .append(".")
                .append(dataset.getDrivingTableName());

        List<DatasetJoinRelDetailRes> datasetJoinRelDetailRes = dataset.getDatasetJoinRels();
        if (CollectionUtils.isNotEmpty(datasetJoinRelDetailRes)) {
            for (DatasetJoinRelDetailRes joinRel : datasetJoinRelDetailRes) {
                String joinCatalogName = joinRel.getTargetCatalogName();
                if (StringUtils.isNotEmpty(joinCatalogName)) {
                    joinCatalogName = joinCatalogName + ".";
                } else {
                    joinCatalogName = "";
                }

                sql.append(" ").append(convertJoinType(joinRel.getJoinType())).append(" ")
                        .append(joinCatalogName)
                        .append(joinRel.getTargetSchemaName())
                        .append(".")
                        .append(joinRel.getTargetTableName())
                        .append(" ON ")
                        .append(joinRel.getJoinCondition());
            }
        }

        // 添加LIMIT子句
        if (previewLimit > 0) {
            sql.append(" LIMIT ").append(previewLimit);
        }

        return sql.toString();
    }

    /**
     * 转换JOIN类型
     */
    private String convertJoinType(String joinType) {
        if (StringUtils.isEmpty(joinType)) {
            throw new BusinessException("JOIN类型不能为空");
        }

        switch (joinType.toLowerCase()) {
            case "innerjoin":
                return "INNER JOIN";
            case "leftjoin":
                return "LEFT JOIN";
            case "rightjoin":
                return "RIGHT JOIN";
        }

        throw new BusinessException("JOIN类型错误");
    }
}
