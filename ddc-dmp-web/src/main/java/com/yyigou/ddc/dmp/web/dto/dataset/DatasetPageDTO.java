package com.yyigou.ddc.dmp.web.dto.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class DatasetPageDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "排除数据集编号列表")
    private List<String> excludeDatasetNoList;

}
