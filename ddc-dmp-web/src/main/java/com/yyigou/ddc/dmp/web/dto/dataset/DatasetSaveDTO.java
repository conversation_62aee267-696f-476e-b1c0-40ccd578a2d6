package com.yyigou.ddc.dmp.web.dto.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DatasetSaveDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "主题编号")
    private String subjectNo;

    @EntityField(name = "数据集编号")
    private String datasetNo;

    @EntityField(name = "数据集编码")
    private String datasetCode;

    @EntityField(name = "数据集名称")
    private String datasetName;

    @EntityField(name = "驱动目录名称")
    private String drivingCatalogName;

    @EntityField(name = "驱动模式名称")
    private String drivingSchemaName;

    @EntityField(name = "驱动表名称")
    private String drivingTableName;

    @EntityField(name = "描述")
    private String description;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "是否删除")
    private Integer deleted;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "创建人名称")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改人编号")
    private String modifyNo;

    @EntityField(name = "修改人名称")
    private String modifyName;

    @EntityField(name = "修改时间")
    private String modifyTime;

    @EntityField(name = "最后操作时间")
    private LocalDateTime opTimestamp;

    @EntityField(name = "数据集连接关系列表")
    private List<DatasetJoinRelSaveDTO> datasetJoinRels;

    @EntityField(name = "数据集字段列表")
    private List<DatasetFieldsSaveDTO> datasetFields;
}
