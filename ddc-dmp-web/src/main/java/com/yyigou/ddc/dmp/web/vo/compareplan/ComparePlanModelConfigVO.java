package com.yyigou.ddc.dmp.web.vo.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.dmp.web.vo.comparemodel.CompareModelRefFieldVO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanModelConfigVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模型编号
     */
    @EntityField(name = "模型编号")
    private String compareModelNo;


    private String enterpriseNo;

    /**
     * 比价指标
     */
    @EntityField(name = "比价指标")
    private List<ComparePlanRefMetricVO> compareMetricList;

    /**
     * 基准指标
     */
    @EntityField(name = "基准指标")
    private List<ComparePlanRefMetricVO> baselineMetricList;

    /**
     * 计算指标集合
     */
    @EntityField(name = "计算指标集合")
    private List<ComparePlanRefCalculatedMetricVO> calculatedMetricList;


    /**
     * 比对对象字段集合
     */
    @EntityField(name = "比对对象字段集合")
    private List<CompareModelRefFieldVO> compareObjectList;

    /**
     * 比对维度字段集合
     */
    @EntityField(name = "比对维度字段集合")
    private List<CompareModelRefFieldVO> compareDimList;
}
