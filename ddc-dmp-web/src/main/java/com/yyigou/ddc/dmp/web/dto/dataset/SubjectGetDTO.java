package com.yyigou.ddc.dmp.web.dto.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SubjectGetDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模型唯一标识
     */
    @EntityField(name = "分析主题唯一标识")
    private String subjectNo;

}
