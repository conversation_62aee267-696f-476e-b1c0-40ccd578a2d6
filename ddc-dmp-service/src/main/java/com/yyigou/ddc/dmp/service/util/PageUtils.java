package com.yyigou.ddc.dmp.service.util;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public class PageUtils {

    public static <V> PageVo<V> convertPageVo(Page<V> page) {
        PageVo<V> voPage = new PageVo();
        voPage.setPageIndex(page.getPageNum());
        voPage.setPageSize(page.getPageSize());
        voPage.setPageCount(page.getPages());
        voPage.setTotal(page.getTotal());
        voPage.setRows(page.getResult());
        return voPage;
    }

    public static <D, V> PageVo<V> convertPageVo(Page<D> page, Function<List<D>, List<V>> function) {
        PageVo<V> voPage = new PageVo();
        voPage.setPageIndex(page.getPageNum());
        voPage.setPageSize(page.getPageSize());
        voPage.setPageCount(page.getPages());
        voPage.setTotal(page.getTotal());
        if (!CollectionUtils.isEmpty(page.getResult())) {
            voPage.setRows(function.apply(page.getResult()));
        } else {
            voPage.setRows(new ArrayList<>());
        }
        return voPage;
    }

    public static <D, V> PageVo<V> convertPageVo(PageVo<D> page, Function<List<D>, List<V>> function) {
        PageVo<V> voPage = new PageVo();
        voPage.setPageIndex(page.getPageIndex());
        voPage.setPageSize(page.getPageSize());
        voPage.setPageCount(page.getPageCount());
        voPage.setTotal(page.getTotal());
        if (!CollectionUtils.isEmpty(page.getRows())) {
            voPage.setRows(function.apply(page.getRows()));
        } else {
            voPage.setRows(new ArrayList<>());
        }
        return voPage;
    }

    public static <V> PageVo<V> convertPageVo(Page page, List<V> rowList) {
        PageVo<V> voPage = new PageVo();
        voPage.setPageIndex(page.getPageNum());
        voPage.setPageSize(page.getPageSize());
        voPage.setPageCount(page.getPages());
        voPage.setTotal(page.getTotal());
        voPage.setRows(rowList);
        return voPage;
    }

    public static <V> PageVo<V> convertPageVo(PageInfo page, Class<V> clazz) {
        List<V> voList = BeanCopyUtil.copyFieldsList(page.getList(), clazz);
        PageVo<V> voPage = new PageVo();
        voPage.setPageIndex(page.getPageNum() == 0 ? 1 : page.getPageNum());
        voPage.setPageSize(page.getPageSize() == 0 ? 20 : page.getPageSize());
        voPage.setPageCount(page.getPages());
        voPage.setTotal(page.getTotal());
        voPage.setRows(voList);
        return voPage;
    }

    /**
     * 只复制pageInfo的分页信息
     *
     * @param page
     * @param <V>
     * @return
     */
    public static <V> PageInfo<V> copyPageInfo(PageInfo page) {
        PageInfo<V> newPage = new PageInfo();
        newPage.setPageNum(page.getPageNum() == 0 ? 1 : page.getPageNum());
        newPage.setPageSize(page.getPageSize() == 0 ? 20 : page.getPageSize());
        newPage.setPages(page.getPages());
        newPage.setTotal(page.getTotal());
        return newPage;
    }

    public static <D, V> PageVo<V> convertPageVo(PageInfo<D> page, Function<List<D>, List<V>> function) {
        PageVo<V> voPage = new PageVo();
        voPage.setPageIndex(page.getPageNum());
        voPage.setPageSize(page.getPageSize());
        voPage.setPageCount(page.getPages());
        voPage.setTotal(page.getTotal());
        if (!CollectionUtils.isEmpty(page.getList())) {
            voPage.setRows(function.apply(page.getList()));
        } else {
            voPage.setRows(new ArrayList<>());
        }
        return voPage;
    }

    /**
     * 只复制pageInfo的分页信息
     *
     * @param page
     * @param <T>
     * @return
     */
    public static <T> PageInfo<T> copyPageInfo(PageInfo page, List<T> list) {
        PageInfo newPage = copyPageInfo(page);
        newPage.setList(list);
        return newPage;
    }


    /**
     * 拷贝page
     *
     * @param page
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K, V> PageVo<V> copyPageVo(PageVo<K> page, Class<V> clazz) {
        PageVo<V> voPage = new PageVo();
        voPage.setPageIndex(page.getPageIndex());
        voPage.setPageSize(page.getPageSize());
        voPage.setPageCount(page.getPageCount());
        voPage.setTotal(page.getTotal());
        if (page.getRows() != null) {
            voPage.setRows(BeanCopyUtil.copyFieldsListForJSON(page.getRows(), clazz));
        }

        return voPage;
    }


    public static <K, V> PageVo<K> convertPageVo(com.baomidou.mybatisplus.extension.plugins.pagination.Page<V> page
            , Class<K> clazz) {
        PageVo<K> voPage = new PageVo();
        voPage.setPageIndex((int) page.getCurrent());
        voPage.setPageSize((int) page.getSize());
        voPage.setPageCount((int) page.getPages());
        voPage.setTotal(page.getTotal());
        voPage.setRows(BeanCopyUtil.copyFieldsList(page.getRecords(), clazz));
        return voPage;
    }
}
