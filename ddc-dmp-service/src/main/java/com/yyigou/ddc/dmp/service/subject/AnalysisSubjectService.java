package com.yyigou.ddc.dmp.service.subject;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.subject.entity.AnalysisSubject;
import com.yyigou.ddc.dmp.model.req.subject.*;
import com.yyigou.ddc.dmp.model.res.subject.SubjectRes;

public interface AnalysisSubjectService extends IService<AnalysisSubject> {
    String saveSubject(SubjectSaveReq subjectSaveReq);

    void deleteSubject(SubjectDeleteReq subjectDeleteReq);

    void startStop(SubjectStartStopReq subjectStartStopReq);

    SubjectRes getSubject(SubjectGetReq subjectGetReq);

    Long saveSubjectDatasetRef(SubjectDatasetRefSaveReq subjectDatasetRefSaveReq);
}