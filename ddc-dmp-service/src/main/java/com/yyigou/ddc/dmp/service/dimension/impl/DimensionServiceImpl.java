package com.yyigou.ddc.dmp.service.dimension.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.StatusEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetFieldDimensionRef;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetFieldDimensionRefMapper;
import com.yyigou.ddc.dmp.dao.dimension.entity.Dimension;
import com.yyigou.ddc.dmp.dao.dimension.entity.DimensionField;
import com.yyigou.ddc.dmp.dao.dimension.mapper.DimensionFieldMapper;
import com.yyigou.ddc.dmp.dao.dimension.mapper.DimensionMapper;
import com.yyigou.ddc.dmp.dao.meta.entity.Tables;
import com.yyigou.ddc.dmp.dao.meta.mapper.MetaLoader;
import com.yyigou.ddc.dmp.model.req.dimension.*;
import com.yyigou.ddc.dmp.model.res.dimension.DatasetFieldDimensionRefRes;
import com.yyigou.ddc.dmp.model.res.dimension.DimensionFieldRes;
import com.yyigou.ddc.dmp.model.res.dimension.DimensionRes;
import com.yyigou.ddc.dmp.service.dimension.DimensionService;
import com.yyigou.ddc.dmp.service.util.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DimensionServiceImpl extends ServiceImpl<DimensionMapper, Dimension> implements DimensionService {
    @Resource
    private DimensionMapper dimensionMapper;

    @Resource
    private DimensionFieldMapper dimensionFieldMapper;

    @Resource
    private DatasetFieldDimensionRefMapper datasetFieldDimensionRefMapper;

    @Resource
    private MetaLoader metaLoader;

    @Override
    @Transactional
    public String saveDimension(DimensionSaveReq dimensionSaveReq) {
        Tables table = metaLoader.getTable(dimensionSaveReq.getSchemaName(), dimensionSaveReq.getTableName());
        if (null == table) {
            throw new BusinessException("维度表不存在");
        }

        Dimension dimension = BeanCopyUtil.copyFields(dimensionSaveReq, Dimension.class);

        List<DimensionFieldSaveReq> dimensionFieldsReq = dimensionSaveReq.getDimensionFields();
        if (CollectionUtils.isEmpty(dimensionFieldsReq)) {
            throw new BusinessException("维度字段不能为空");
        }

        List<DimensionField> dimensionFields = BeanCopyUtil.copyFieldsList(dimensionFieldsReq, DimensionField.class);

        if (StringUtils.isEmpty(dimension.getDimensionNo())) {
            //新增
            dimension.setDimensionNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<Dimension>lambdaQuery()
                    .eq(Dimension::getEnterpriseNo, dimension.getEnterpriseNo())
                    .eq(Dimension::getDimensionNo, dimension.getDimensionNo())
                    .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("维度不存在");
            }

            Dimension toDeleteDimension = new Dimension();
            toDeleteDimension.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteDimension, Wrappers.<Dimension>lambdaQuery()
                    .eq(Dimension::getEnterpriseNo, dimension.getEnterpriseNo())
                    .eq(Dimension::getDimensionNo, dimension.getDimensionNo())
                    .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(dimension);

        DimensionField toDeleteDimensionField = new DimensionField();
        toDeleteDimensionField.setDeleted(DeletedEnum.DELETED.getValue());
        dimensionFieldMapper.update(toDeleteDimensionField, Wrappers.<DimensionField>lambdaQuery()
                .eq(DimensionField::getEnterpriseNo, dimension.getEnterpriseNo())
                .eq(DimensionField::getDimensionNo, dimension.getDimensionNo())
                .eq(DimensionField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        for (DimensionField dimensionField : dimensionFields) {
            dimensionField.setEnterpriseNo(dimension.getEnterpriseNo());
            dimensionField.setDimensionNo(dimension.getDimensionNo());
        }

        dimensionFieldMapper.insert(dimensionFields);

        return dimension.getDimensionNo();
    }

    @Override
    public void deleteDimension(DimensionDeleteReq dimensionDeleteReq) {
        //TODO shenbin 依赖项校验

        Dimension toDeleteDimension = new Dimension();
        toDeleteDimension.setDeleted(DeletedEnum.DELETED.getValue());
        update(toDeleteDimension, Wrappers.<Dimension>lambdaQuery()
                .eq(Dimension::getEnterpriseNo, dimensionDeleteReq.getEnterpriseNo())
                .eq(Dimension::getDimensionNo, dimensionDeleteReq.getDimensionNo())
                .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
    }

    @Override
    public void startStop(DimensionStartStopReq dimensionStartStopReq) {
        ValidatorUtil.checkEmptyThrowEx(dimensionStartStopReq.getEnterpriseNo(), "租户编号不能为空");
        ValidatorUtil.checkEmptyThrowEx(dimensionStartStopReq.getDimensionNo(), "维度编号不能为空");
        ValidatorUtil.checkEmptyThrowEx(dimensionStartStopReq.getStatus(), "启停状态不能为空");
        StatusEnum status = StatusEnum.getByValue(dimensionStartStopReq.getStatus());
        ValidatorUtil.checkTrueThrowEx(status == null, "启停状态不正确");

        Dimension toUpdateDimension = new Dimension();
        toUpdateDimension.setStatus(dimensionStartStopReq.getStatus());
        update(toUpdateDimension, Wrappers.<Dimension>lambdaQuery()
                .eq(Dimension::getEnterpriseNo, dimensionStartStopReq.getEnterpriseNo())
                .eq(Dimension::getDimensionNo, dimensionStartStopReq.getDimensionNo())
                .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

    }

    @Override
    public DimensionRes getDimension(DimensionGetReq dimensionGetReq) {
        Dimension dimension = getOne(Wrappers.<Dimension>lambdaQuery()
                .eq(Dimension::getEnterpriseNo, dimensionGetReq.getEnterpriseNo())
                .eq(Dimension::getDimensionNo, dimensionGetReq.getDimensionNo())
                .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        ValidatorUtil.checkEmptyThrowEx(dimension, "维度不存在");

        List<DimensionRes> dimensionRes = wrapDimension(dimensionGetReq.getEnterpriseNo(), Collections.singletonList(dimension));
        return dimensionRes.get(0);
    }

    @Override
    public List<DimensionRes> getDimensionList(DimensionGetListReq dimensionGetListReq) {
        List<Dimension> dimensions = dimensionMapper.selectList(Wrappers.<Dimension>lambdaQuery()
                .eq(Dimension::getEnterpriseNo, dimensionGetListReq.getEnterpriseNo())
                .in(Dimension::getDimensionNo, dimensionGetListReq.getDimensionNoList())
                .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if (CollectionUtils.isEmpty(dimensions)) {
            return Collections.emptyList();
        }

        return wrapDimension(dimensionGetListReq.getEnterpriseNo(), dimensions);
    }

    @Override
    public PageVo<DimensionRes> queryListPage(DimensionPageReq dimensionPageReq, PageDto pageDTO) {
        Page<Dimension> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), org.apache.commons.lang3.StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());

        dimensionMapper.selectList(Wrappers.<Dimension>lambdaQuery()
                .eq(Dimension::getEnterpriseNo, dimensionPageReq.getEnterpriseNo())
                .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        return PageUtils.convertPageVo(page,
                data -> wrapDimension(dimensionPageReq.getEnterpriseNo(), data)
        );
    }

    private List<DimensionRes> wrapDimension(String enterpriseNo, List<Dimension> dimensions) {
        if (CollectionUtils.isEmpty(dimensions)) {
            return Collections.emptyList();
        }

        List<DimensionRes> dimensionResList = BeanCopyUtil.copyFieldsList(dimensions, DimensionRes.class);

        Set<String> dimensionNoSet = dimensionResList.stream().map(DimensionRes::getDimensionNo).collect(Collectors.toSet());
        List<DimensionField> dimensionFields = dimensionFieldMapper.selectList(Wrappers.<DimensionField>lambdaQuery()
                .eq(DimensionField::getEnterpriseNo, enterpriseNo)
                .in(DimensionField::getDimensionNo, dimensionNoSet)
                .eq(DimensionField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<DimensionField>> dimensionNoToFieldMap = dimensionFields.stream().collect(Collectors.groupingBy(DimensionField::getDimensionNo));

        List<DatasetFieldDimensionRef> datasetFieldDimensionRefs = datasetFieldDimensionRefMapper.selectList(Wrappers.<DatasetFieldDimensionRef>lambdaQuery()
                .eq(DatasetFieldDimensionRef::getEnterpriseNo, enterpriseNo)
                .in(DatasetFieldDimensionRef::getDimensionNo, dimensionNoSet)
                .eq(DatasetFieldDimensionRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<DatasetFieldDimensionRef>> dimensionNoToDatasetFieldDimensionRefMap = datasetFieldDimensionRefs.stream().collect(Collectors.groupingBy(DatasetFieldDimensionRef::getDimensionNo));

        for (DimensionRes dimensionRes : dimensionResList) {
            List<DimensionField> dimensionFieldList = dimensionNoToFieldMap.get(dimensionRes.getDimensionNo());
            if (CollectionUtils.isNotEmpty(dimensionFieldList)) {
                List<DimensionFieldRes> dimensionFieldRes = BeanCopyUtil.copyFieldsList(dimensionFieldList, DimensionFieldRes.class);
                dimensionRes.setDimensionFields(dimensionFieldRes);
            }

            List<DatasetFieldDimensionRef> datasetFieldDimensionRefList = dimensionNoToDatasetFieldDimensionRefMap.get(dimensionRes.getDimensionNo());
            if (CollectionUtils.isNotEmpty(datasetFieldDimensionRefList)) {
                List<DatasetFieldDimensionRefRes> datasetFieldDimensionRefRes = BeanCopyUtil.copyFieldsList(datasetFieldDimensionRefList, DatasetFieldDimensionRefRes.class);
                dimensionRes.setDatasetFieldDimensionRefResList(datasetFieldDimensionRefRes);
            }
        }

        return dimensionResList;
    }

    @Override
    public List<DatasetFieldDimensionRefRes> queryDatasetFieldDimensionRef(DatasetFieldDimensionRefQueryReq datasetFieldDimensionRefQueryReq) {
        List<DatasetFieldDimensionRef> datasetFieldDimensionRefs = datasetFieldDimensionRefMapper.selectList(Wrappers.<DatasetFieldDimensionRef>lambdaQuery()
                .eq(DatasetFieldDimensionRef::getEnterpriseNo, datasetFieldDimensionRefQueryReq.getEnterpriseNo())
                .in(DatasetFieldDimensionRef::getDatasetNo, datasetFieldDimensionRefQueryReq.getDatasetNoList())
                .eq(datasetFieldDimensionRefQueryReq.getStatus() != null, DatasetFieldDimensionRef::getStatus, datasetFieldDimensionRefQueryReq.getStatus())
                .notIn(org.apache.commons.collections.CollectionUtils.isNotEmpty(datasetFieldDimensionRefQueryReq.getExcludeDimensionNoList()), DatasetFieldDimensionRef::getDimensionNo, datasetFieldDimensionRefQueryReq.getExcludeDimensionNoList())
                .eq(DatasetFieldDimensionRef::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        List<DatasetFieldDimensionRefRes> datasetFieldDimensionRefRes = BeanCopyUtil.copyFieldsList(datasetFieldDimensionRefs, DatasetFieldDimensionRefRes.class);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(datasetFieldDimensionRefRes)) {
            Set<String> dimensionNoList = datasetFieldDimensionRefRes.stream().map(DatasetFieldDimensionRefRes::getDimensionNo).collect(Collectors.toSet());
            List<Dimension> dimensions = dimensionMapper.selectList(Wrappers.<Dimension>lambdaQuery()
                    .eq(Dimension::getEnterpriseNo, datasetFieldDimensionRefQueryReq.getEnterpriseNo())
                    .in(Dimension::getDimensionNo, dimensionNoList)
                    .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            Map<String, Dimension> dimensionNoToDimensionMap = dimensions.stream().collect(Collectors.toMap(Dimension::getDimensionNo, Function.identity()));

            for (DatasetFieldDimensionRefRes datasetFieldDimensionRefQueryRe : datasetFieldDimensionRefRes) {
                Dimension dimension = dimensionNoToDimensionMap.get(datasetFieldDimensionRefQueryRe.getDimensionNo());
                if (dimension != null) {
                    datasetFieldDimensionRefQueryRe.setDimensionCode(dimension.getDimensionCode());
                    datasetFieldDimensionRefQueryRe.setDimensionName(dimension.getDimensionName());
                }
            }
        }

        return datasetFieldDimensionRefRes;
    }

    @Override
    public Long saveDatasetFieldDimensionRef(DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq) {
        datasetFieldDimensionRefMapper.update(null, Wrappers.<DatasetFieldDimensionRef>lambdaUpdate()
                .set(DatasetFieldDimensionRef::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(DatasetFieldDimensionRef::getEnterpriseNo, datasetFieldDimensionRefSaveReq.getEnterpriseNo())
                .eq(DatasetFieldDimensionRef::getDatasetNo, datasetFieldDimensionRefSaveReq.getDatasetNo())
                .eq(DatasetFieldDimensionRef::getDatasetFieldNo, datasetFieldDimensionRefSaveReq.getDatasetFieldNo())
                .eq(DatasetFieldDimensionRef::getDimensionNo, datasetFieldDimensionRefSaveReq.getDimensionNo())
                .eq(DatasetFieldDimensionRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        DatasetFieldDimensionRef datasetFieldDimensionRef = new DatasetFieldDimensionRef();
        datasetFieldDimensionRef.setEnterpriseNo(datasetFieldDimensionRefSaveReq.getEnterpriseNo());
        datasetFieldDimensionRef.setDatasetNo(datasetFieldDimensionRefSaveReq.getDatasetNo());
        datasetFieldDimensionRef.setDatasetFieldNo(datasetFieldDimensionRefSaveReq.getDatasetFieldNo());
        datasetFieldDimensionRef.setDimensionNo(datasetFieldDimensionRefSaveReq.getDimensionNo());
        datasetFieldDimensionRef.setDescription(datasetFieldDimensionRefSaveReq.getDescription());

        datasetFieldDimensionRefMapper.insert(datasetFieldDimensionRef);

        return datasetFieldDimensionRef.getId();
    }
}
