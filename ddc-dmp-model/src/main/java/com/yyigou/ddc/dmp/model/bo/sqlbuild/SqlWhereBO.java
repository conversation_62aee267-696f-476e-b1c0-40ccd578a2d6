package com.yyigou.ddc.dmp.model.bo.sqlbuild;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/11
 */
@Data
@Builder
public class SqlWhereBO {

    /**
     * 模型字段编码
     */
    private String fieldCode;

    /**
     * 模型编号
     */
    private String tableAlias;


    /**
     * 操作类型
     * 根据操作类型，从value或者valueList中获取参数值
     *
     */
    private String operator;


    private Object value;

    private List<Object> valueList;

}
