package com.yyigou.ddc.dmp.common.util;

import cn.hutool.core.collection.ListUtil;
import com.yyigou.ddc.dmp.common.enums.SqlJoinTypeEnum;
import com.yyigou.ddc.dmp.common.context.SqlExecuteContext;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2025/07/11
 */
@Slf4j
public class SqlBuilderUtilTest {


    @Test
    public void buildSelectSql_test() throws JSQLParserException {
        SqlExecuteContext sqlExecuteContext = new SqlExecuteContext();

        // 设置主表
        sqlExecuteContext.setMainTable(SqlTableBO.builder().tableName("base").build());

        // 设置 SELECT 字段
        sqlExecuteContext.setSelectList(Arrays.asList(
                SqlFieldBO.builder().tableAlias("base").fieldCode("manage_org_no").fieldAlias("管理组织编号").build(),
                SqlFieldBO.builder().tableAlias("base").fieldCode("use_org_no").fieldAlias("使用组织编号").build(),
                SqlFieldBO.builder().tableAlias("base").fieldCode("sku_code").fieldAlias("产品编码").build(),
                SqlFieldBO.builder().tableAlias("basic").fieldCode("goods_name").fieldAlias("产品名称").build(),
                SqlFieldBO.builder().tableAlias("biz").fieldCode("goods_line_no").fieldAlias("产品线编号").build()
//                SqlFieldBO.builder().modelNo("biz").fieldCode("id").fieldAlias("id计数").isAggregation(true).build()
        ));

        // 设置 WHERE 条件
        sqlExecuteContext.setWhereList(Arrays.asList(
                SqlWhereBO.builder()
                        .tableAlias("base")
                        .fieldCode("deleted")
                        .operator("=")
                        .value(1)
                        .build(),
                SqlWhereBO.builder()
                        .tableAlias("basic")
                        .fieldCode("deleted")
                        .operator("=")
                        .value(1)
                        .build(),
                SqlWhereBO.builder()
                        .tableAlias("biz")
                        .fieldCode("deleted")
                        .operator("=")
                        .value(1)
                        .build(),
                SqlWhereBO.builder()
                        .tableAlias("base")
                        .fieldCode("enterprise_no")
                        .operator("=")
                        .value("2000002")
                        .build(),
                // 尝试sql注入
                SqlWhereBO.builder()
                        .tableAlias("base")
                        .fieldCode("enterprise_no")
                        .operator("=")
                        .value("2000002 or (1=1)")
                        .build(),
                SqlWhereBO.builder()
                        .tableAlias("base")
                        .fieldCode("sku_no")
                        .operator("in")
                        .valueList(ListUtil.toList("1010000070047","1010000070046","","1010000070035"))
                        .build()
        ));

        // 设置 JOIN 表
        sqlExecuteContext.setJoinList(Arrays.asList(
                // LEFT JOIN table2 ON table1.id = table2.t1_id
                SqlJoinBO.builder()
                        .tableAlias("basic")
                        .joinType(SqlJoinTypeEnum.INNER_JOIN.getJoinType())
                        .joinConditionList(ListUtil.toList(
                                SqlConditionOnBO.builder()
                                        .leftFieldCode("sku_code")
                                        .leftTableName("base")
                                        .rightFieldCode("sku_code")
                                        .rightTableName("basic")
                                        .build(),
                                SqlConditionOnBO.builder()
                                        .leftFieldCode("enterprise_no")
                                        .leftTableName("base")
                                        .rightFieldCode("enterprise_no")
                                        .rightTableName("basic")
                                        .build()
                        ))
                        .build(),

                // INNER JOIN table3 ON table2.id = table3.t2_id
                SqlJoinBO.builder()
                        .tableAlias("biz")
                        .joinType(SqlJoinTypeEnum.INNER_JOIN.getJoinType())
                        .joinConditionList(ListUtil.toList(
                                SqlConditionOnBO.builder()
                                        .leftFieldCode("sku_code")
                                        .leftTableName("base")
                                        .rightFieldCode("sku_code")
                                        .rightTableName("biz")
                                        .build(),
                                SqlConditionOnBO.builder()
                                        .leftFieldCode("enterprise_no")
                                        .leftTableName("base")
                                        .rightFieldCode("enterprise_no")
                                        .rightTableName("biz")
                                        .build()
                        ))
                        .build()
        ));

        // 设置 GROUP BY
        sqlExecuteContext.setGroupByList(Arrays.asList(
                SqlFieldBO.builder().tableAlias("base").fieldCode("manage_org_no").fieldAlias("管理组织编号").build(),
                SqlFieldBO.builder().tableAlias("base").fieldCode("use_org_no").fieldAlias("使用组织编号").build(),
                SqlFieldBO.builder().tableAlias("base").fieldCode("sku_code").fieldAlias("产品编码").build(),
                SqlFieldBO.builder().tableAlias("basic").fieldCode("goods_name").fieldAlias("产品名称").build(),
                SqlFieldBO.builder().tableAlias("biz").fieldCode("goods_line_no").fieldAlias("产品线编号").build()
        ));

        // 设置 ORDER BY
        sqlExecuteContext.setOrderByList(Collections.singletonList(
                SqlOrderByBO.builder()
                        .tableAlias("base")
                        .fieldCode("id")
                        .asc(true)
                        .build()
        ));
        log.info(SqlBuilderUtil.buildSelectSql(sqlExecuteContext));
    }

}
