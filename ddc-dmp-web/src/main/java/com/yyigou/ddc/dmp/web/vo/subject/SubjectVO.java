package com.yyigou.ddc.dmp.web.vo.subject;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SubjectVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 租户编号
     */
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    /**
     * 分析主题唯一标识
     */
    @EntityField(name = "分析主题唯一标识")
    private String subjectNo;

    /**
     * 分析主题名称
     */
    @EntityField(name = "分析主题名称")
    private String subjectName;

    /**
     * 主题描述
     */
    @EntityField(name = "主题描述")
    private String description;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @EntityField(name = "数据是否有效")
    private Integer status;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @EntityField(name = "是否删除")
    private Integer deleted;

    /**
     * 创建人编号
     */
    @EntityField(name = "创建人编号")
    private String createNo;

    /**
     * 创建人名称
     */
    @EntityField(name = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @EntityField(name = "创建时间")
    private String createTime;

    /**
     * 修改人编号
     */
    @EntityField(name = "修改人编号")
    private String modifyNo;

    /**
     * 修改人名称
     */
    @EntityField(name = "修改人名称")
    private String modifyName;

    /**
     * 修改时间
     */
    @EntityField(name = "修改时间")
    private String modifyTime;

    /**
     * 最后操作时间
     */
    @EntityField(name = "最后操作时间")
    private LocalDateTime opTimestamp;

    @EntityField(name = "数据集编号列表")
    private List<String> datasetNoList;
}

