package com.yyigou.ddc.dmp.service.comparemodel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelChangeStatusReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelGetReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelPageQueryReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.bo.compareplan.CompareModelFullDetailBO;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelPageRes;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
public interface CompareModelService extends IService<CompareModel> {

    CompareModelPageRes save(CompareModelSaveReq compareModelSaveReq);

    Boolean update(CompareModelSaveReq compareModelSaveReq);

    Boolean delete(CompareModelGetReq deleteReq);

    Boolean changeStatus(CompareModelChangeStatusReq changeStatusReq);

    CompareModelGetRes get(CompareModelGetReq getReq);

    PageVo<CompareModelPageRes> pageQuery(CompareModelPageQueryReq pageReq);

    CompareModel checkModelExist(String compareModelNo);

    CompareModelFullDetailBO getCompareModelDetail(String compareModelNo);
}
