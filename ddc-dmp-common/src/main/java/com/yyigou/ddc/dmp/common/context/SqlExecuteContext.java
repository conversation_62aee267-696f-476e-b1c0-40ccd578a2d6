package com.yyigou.ddc.dmp.common.context;

import com.yyigou.ddc.dmp.model.bo.sqlbuild.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * SQL 执行上下文
 * <AUTHOR>
 * @date 2025/07/11
 */
@Data
public class SqlExecuteContext {


    /**
     * 查询字段列表
     */
    private List<SqlFieldBO> selectList;

    private SqlTableBO mainTable;

    /**
     * 多表关联关系
     */
    private List<SqlJoinBO> joinList;

    /**
     * 查询条件列表
     */
    private List<SqlWhereBO> whereList;

    /**
     * 分组字段列表
     */
    private List<SqlFieldBO> groupByList;


    private List<SqlOrderByBO> orderByList;

    /**
     * 原始变量列表
     */
    private Map<String, Object> variableMap;

    /**
     * 处理过的变量列表
     */
    private Map<String, Object> processedVariableMap;

    private String enterpriseNo;




}
