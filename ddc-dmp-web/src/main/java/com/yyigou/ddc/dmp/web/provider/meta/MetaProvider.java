package com.yyigou.ddc.dmp.web.provider.meta;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.res.meta.ColumnsRes;
import com.yyigou.ddc.dmp.model.res.meta.SchemataRes;
import com.yyigou.ddc.dmp.model.res.meta.TablesRes;
import com.yyigou.ddc.dmp.service.meta.MetaService;
import com.yyigou.ddc.dmp.web.api.meta.MetaAPI;
import com.yyigou.ddc.dmp.web.dto.meta.ColumnsQueryDTO;
import com.yyigou.ddc.dmp.web.dto.meta.TablesQueryDTO;
import com.yyigou.ddc.dmp.web.vo.meta.ColumnsVO;
import com.yyigou.ddc.dmp.web.vo.meta.SchemataVO;
import com.yyigou.ddc.dmp.web.vo.meta.TablesVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Interface(name = "数据源")
@Component("metaProvider")
public class MetaProvider extends ServiceBaseAbstract implements MetaAPI {
    @Resource
    private MetaService metaService;

    @Override
    @Method(aliasName = "ddc.dmp.meta.querySchemas", name = "元数据Schema查询", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<List<SchemataVO>> querySchemas() {
        try {
            List<SchemataRes> schemataRes = metaService.loadSchematas();

            List<SchemataVO> schemataVOS = BeanCopyUtil.copyFieldsList(schemataRes, SchemataVO.class);
            return CallResult.success(schemataVOS);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.meta.queryTables", name = "元数据Table查询", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<List<TablesVO>> queryTables(TablesQueryDTO params) {
        try {
            String env = System.getProperty("spring.profiles.active");
            String schemaName = "dwd";
            if (!"prod".equals(env)) {
                schemaName = env + "_" + schemaName;
            }
            List<TablesRes> tablesRes = metaService.loadTables(schemaName);

            List<TablesVO> tablesVOS = BeanCopyUtil.copyFieldsList(tablesRes, TablesVO.class);
            return CallResult.success(tablesVOS);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.meta.queryColumns", name = "元数据Column查询", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<List<ColumnsVO>> queryColumns(ColumnsQueryDTO params) {
        try {
            List<ColumnsRes> columnsRes = metaService.loadColumns(params.getSchemaName(), params.getTableName());

            List<ColumnsVO> columnsVOS = BeanCopyUtil.copyFieldsList(columnsRes, ColumnsVO.class);
            return CallResult.success(columnsVOS);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
