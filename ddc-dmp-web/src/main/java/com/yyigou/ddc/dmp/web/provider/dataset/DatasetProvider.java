package com.yyigou.ddc.dmp.web.provider.dataset;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.web.api.dataset.DatasetAPI;
import com.yyigou.ddc.dmp.web.dto.dataset.*;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetPreviewVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetValidateVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Interface(name = "数据集")
@Component("datasetProvider")
public class DatasetProvider extends ServiceBaseAbstract implements DatasetAPI {
    @Resource
    private DatasetService datasetService;

    /**
     * 保存数据集
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.save", name = "保存数据集", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<DatasetVO> save(DatasetSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(params, DatasetSaveReq.class);
//        datasetSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetSaveReq.setEnterpriseNo("2000002");
            String datasetNo = datasetService.saveDataset(datasetSaveReq);

            DatasetVO datasetVO = new DatasetVO();
            datasetVO.setDatasetNo(datasetNo);
            return CallResult.success(datasetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.dataset.queryListPage", name = "查询数据集", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<PageVo<DatasetVO>> queryListPage(DatasetPageDTO params, PageDto pageParams) {
        try {
            DatasetPageReq datasetPageReq = BeanCopyUtil.copyFieldsByJson(params, DatasetPageReq.class);
//        datasetQueryReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            datasetPageReq.setEnterpriseNo("2000002");
            PageVo<DatasetDetailRes> datasetDetailResPageVo = datasetService.queryListPage(datasetPageReq, pageParams);

            List<DatasetVO> datasetVOS = BeanCopyUtil.copyFieldsList(datasetDetailResPageVo.getRows(), DatasetVO.class);
            PageVo<DatasetVO> pageVo = new PageVo<>(datasetDetailResPageVo.getPageIndex(), datasetDetailResPageVo.getPageSize(), datasetDetailResPageVo.getTotal(), datasetVOS);
            return CallResult.success(pageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.dataset.getList", name = "查询数据集", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<List<DatasetVO>> getList(DatasetGetListDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNoList(), "数据集编号不能为空");

            DatasetGetListReq datasetGetListReq = BeanCopyUtil.copyFieldsByJson(params, DatasetGetListReq.class);
//        datasetGetListReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            datasetGetListReq.setEnterpriseNo("2000002");
            List<DatasetDetailRes> datasetDetailResList = datasetService.getDatasetList(datasetGetListReq);

            if (CollectionUtils.isEmpty(datasetDetailResList)) {
                return CallResult.success(Collections.emptyList());
            }

            List<DatasetVO> datasetVOS = BeanCopyUtil.copyFieldsList(datasetDetailResList, DatasetVO.class);
            return CallResult.success(datasetVOS);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    /**
     * 查看数据集
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.get", name = "查看数据集", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetVO> get(DatasetGetDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetGetReq datasetGetReq = BeanCopyUtil.copyFieldsByJson(params, DatasetGetReq.class);
//        datasetGetReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetGetReq.setEnterpriseNo("2000002");
            DatasetDetailRes datasetDetailRes = datasetService.getDataset(datasetGetReq);

            if (null == datasetDetailRes) {
                throw new BusinessException("数据集不存在");
            }

            DatasetVO datasetVO = BeanCopyUtil.copyFields(datasetDetailRes, DatasetVO.class);
            return CallResult.success(datasetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 预览数据集数据
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.preview", name = "预览数据集数据", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetPreviewVO> previewData(DatasetPreviewDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetPreviewReq datasetPreviewReq = BeanCopyUtil.copyFieldsByJson(params, DatasetPreviewReq.class);
//        datasetPreviewReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetPreviewReq.setEnterpriseNo("2000002");
            DatasetPreviewRes datasetPreviewRes = datasetService.previewData(datasetPreviewReq);

            DatasetPreviewVO datasetPreviewVO = BeanCopyUtil.copyFields(datasetPreviewRes, DatasetPreviewVO.class);
            return CallResult.success(datasetPreviewVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 校验数据集配置
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.validate", name = "校验数据集配置", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetValidateVO> validateDataset(DatasetValidateDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(params, DatasetSaveReq.class);
//        datasetValidateReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetSaveReq.setEnterpriseNo("2000002");

            DatasetValidateVO datasetValidateVO = new DatasetValidateVO();
            datasetValidateVO.setDatasetNo(datasetSaveReq.getDatasetNo());

            try {
                datasetService.validateDataset(datasetSaveReq);
                datasetValidateVO.setValid(true);
            } catch (Exception e) {
                datasetValidateVO.setValid(false);
                datasetValidateVO.setMessage(e.getMessage());
            }
            return CallResult.success(datasetValidateVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
