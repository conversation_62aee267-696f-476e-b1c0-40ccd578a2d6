package com.yyigou.ddc.dmp.service.metric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.dao.metric.entity.Metric;
import com.yyigou.ddc.dmp.model.req.metric.*;
import com.yyigou.ddc.dmp.model.res.metric.MetricRes;

import java.util.List;

public interface MetricService extends IService<Metric> {
    String saveMetric(MetricSaveReq metricSaveReq);

    PageVo<MetricRes> queryListPage(MetricPageReq metricPageReq, PageDto pageDto);

    MetricRes getMetric(MetricGetReq metricGetReq);

    List<MetricRes> getMetricList(MetricGetListReq metricGetListReq);

    List<MetricRes> getMetricByDatasetNo(MetricGetListByDatasetNoReq metricGetListByDatasetNoReq);
}