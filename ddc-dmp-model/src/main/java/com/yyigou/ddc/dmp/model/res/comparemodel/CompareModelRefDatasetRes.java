package com.yyigou.ddc.dmp.model.res.comparemodel;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelRefDatasetRes implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * id，更新用到该字段
     */
    private Long id;

    /**
     * 数据集编号
     */
    private String datasetNo;

    /**
     * 数据集名称
     */
    private String datasetName;

    /**
     * 关联类型 innerjoin,leftjoin,rightjoin
     */
    private String joinType;

    /**
     * 连接方式名称
     */
    private String joinTypeName;


}
