package com.yyigou.ddc.dmp.web.dto.dataset;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DatasetSaveDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String enterpriseNo;
    private String subjectNo;
    private String datasetNo;
    private String datasetCode;
    private String datasetName;
    private String drivingCatalogName;
    private String drivingSchemaName;
    private String drivingTableName;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;

    private List<DatasetJoinRelSaveDTO> datasetJoinRels;
    private List<DatasetFieldsSaveDTO> datasetFields;
}
