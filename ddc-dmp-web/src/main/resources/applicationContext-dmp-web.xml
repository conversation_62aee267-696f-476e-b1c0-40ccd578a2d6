<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
        http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">


<!--    &lt;!&ndash;配置热更新&ndash;&gt;-->
<!--    <bean id="zkMonitorClient" class="com.yyigou.ddc.common.zkmonitor.ZkMonitorClient" >-->
<!--        <property name="monitObjectScanPackage" value="com.yyigou" />-->
<!--    </bean>-->

    <!--dubbo服务配置-->
    <dubbo:application name="service-ddc-dmp" owner="yyigou" organization="yyigou.ddc"/>
    <dubbo:registry address="${common.dubbo.provider.zkurl}" file="/tmp/dubbo-cache/service-ddc-dmp/default.cache" />
    <dubbo:protocol name="dubbo" port="${dubbo.provider.dubbo.port:20301}"/>
    <dubbo:consumer timeout="${common.dubbo.provider.timeout}" retries="0"/>



    <!-- API文档发布设置，开发环境中需配置此项用于自动发布API文档，开发电脑及其它环境不需要设置，或将isPublish设为false -->
    <bean id="apiDocumentConfig" class="com.yyigou.ddc.common.dubbo.registry.zookeeper.APIDocumentConfig">
        <property name="isPublish" value="true"/>
        <!-- 只有是以下指定IP的服务器才能发布，如不指定则不限制发布机器 -->
        <property name="publisherIPAddress" value=""/>
        <property name="discoveryUrl" value="${common.dubbo.provider.zkurl}"/>
    </bean>

    <dubbo:service id="reportTemplateAPI" interface="com.yyigou.ddc.dmp.web.api.ReportTemplateAPI" ref="reportTemplateProvider" protocol="dubbo" />
    <dubbo:service id="compareModelAPI" interface="com.yyigou.ddc.dmp.web.api.compare.CompareModelAPI" ref="compareModelProvider" protocol="dubbo" />
    <dubbo:service id="comparePlanAPI" interface="com.yyigou.ddc.dmp.web.api.compare.ComparePlanAPI" ref="comparePlanProvider" protocol="dubbo" />

    <dubbo:service id="metaAPI" interface="com.yyigou.ddc.dmp.web.api.meta.MetaAPI" ref="metaProvider" protocol="dubbo" />
    <dubbo:service id="datasetAPI" interface="com.yyigou.ddc.dmp.web.api.dataset.DatasetAPI" ref="datasetProvider" protocol="dubbo" />
    <dubbo:service id="dimensionAPI" interface="com.yyigou.ddc.dmp.web.api.dimension.DimensionAPI" ref="dimensionProvider" protocol="dubbo" />
    <dubbo:service id="metricAPI" interface="com.yyigou.ddc.dmp.web.api.metric.MetricAPI" ref="metricProvider" protocol="dubbo" />
    <dubbo:service id="analysisSubjectAPI" interface="com.yyigou.ddc.dmp.web.api.subject.AnalysisSubjectAPI" ref="analysisSubjectProvider" protocol="dubbo" />


    <!-- dubbo服务提供 -->
    <import resource="classpath*:ddc-dmp-manager.xml"/>
</beans>