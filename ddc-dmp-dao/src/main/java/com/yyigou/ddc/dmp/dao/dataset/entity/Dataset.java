package com.yyigou.ddc.dmp.dao.dataset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("dmp_dataset")
public class Dataset {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String enterpriseNo;
    private String datasetNo;
    private String datasetCode;
    private String datasetName;
    private String drivingCatalogName;
    private String drivingSchemaName;
    private String drivingTableName;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}