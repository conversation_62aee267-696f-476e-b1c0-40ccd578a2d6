package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetValidateRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.*;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetPreviewVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetValidateVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 数据集前端控制器
 */
@RestController
@RequestMapping("/dataset")
public class DatasetController {
    @Resource
    private DatasetService datasetService;

    /**
     * 保存数据集
     */
    @PostMapping("/save")
    public DatasetVO save(@RequestBody CommonDTO<DatasetSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), DatasetSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetSaveReq.setEnterpriseNo("2000002");
        String datasetNo = datasetService.saveDataset(datasetSaveReq);

        DatasetVO datasetVO = new DatasetVO();
        datasetVO.setDatasetNo(datasetNo);
        return datasetVO;
    }

    /**
     * 查询数据集
     */
    @GetMapping("/queryListPage")
    public PageVo<DatasetVO> queryListPage(@RequestBody CommonDTO<DatasetPageDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);

        DatasetPageReq datasetPageReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), DatasetPageReq.class);
//        datasetQueryReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetPageReq.setEnterpriseNo("2000002");
        PageDto pageDto = new PageDto();

        PageVo<DatasetDetailRes> datasetDetailResPageVo = datasetService.queryListPage(datasetPageReq, pageDto);

        List<DatasetVO> datasetVOS = BeanCopyUtil.copyFieldsList(datasetDetailResPageVo.getRows(), DatasetVO.class);
        PageVo<DatasetVO> pageVo = new PageVo<>(datasetDetailResPageVo.getPageIndex(), datasetDetailResPageVo.getPageSize(), datasetDetailResPageVo.getTotal(), datasetVOS);
        return pageVo;
    }

    /**
     * 查看数据集
     */
    @GetMapping("/get")
    public DatasetVO get(@RequestBody CommonDTO<DatasetGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);
        ValidatorUtil.checkEmptyThrowEx(getDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetGetReq datasetGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), DatasetGetReq.class);
//        detailGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetGetReq.setEnterpriseNo("2000002");
        DatasetDetailRes datasetDetailRes = datasetService.getDataset(datasetGetReq);

        if (null == datasetDetailRes) {
            throw new BusinessException("数据集不存在");
        }

        DatasetVO datasetVO = BeanCopyUtil.copyFields(datasetDetailRes, DatasetVO.class);
        return datasetVO;
    }


    /**
     * 查看数据集
     */
    @GetMapping("/getList")
    public List<DatasetVO> getList(@RequestBody CommonDTO<DatasetGetListDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);
        ValidatorUtil.checkEmptyThrowEx(getDto.getParams().getDatasetNoList(), "数据集编号不能为空");

        DatasetGetListReq datasetGetListReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), DatasetGetListReq.class);
//        datasetGetListReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetGetListReq.setEnterpriseNo("2000002");
        List<DatasetDetailRes> datasetDetailResList = datasetService.getDatasetList(datasetGetListReq);

        if (CollectionUtils.isEmpty(datasetDetailResList)) {
            return Collections.emptyList();
        }

        return BeanCopyUtil.copyFieldsList(datasetDetailResList, DatasetVO.class);
    }

    /**
     * 预览数据集数据
     */
    @PostMapping("/preview")
    public DatasetPreviewVO previewData(@RequestBody CommonDTO<DatasetPreviewDTO> previewDto) {
        CommonParamsUtil.validateCommonDTO(previewDto);
        ValidatorUtil.checkEmptyThrowEx(previewDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetPreviewReq datasetPreviewReq = BeanCopyUtil.copyFieldsByJson(previewDto.getParams(), DatasetPreviewReq.class);
//        datasetPreviewReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetPreviewReq.setEnterpriseNo("2000002");
        DatasetPreviewRes datasetPreviewRes = datasetService.previewData(datasetPreviewReq);

        DatasetPreviewVO datasetPreviewVO = BeanCopyUtil.copyFields(datasetPreviewRes, DatasetPreviewVO.class);
        return datasetPreviewVO;
    }

    /**
     * 校验数据集配置
     */
    @PostMapping("/validate")
    public DatasetValidateVO validateDataset(@RequestBody CommonDTO<DatasetSaveDTO> validateDto) {
        CommonParamsUtil.validateCommonDTO(validateDto);

        DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(validateDto.getParams(), DatasetSaveReq.class);
//        datasetValidateReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetSaveReq.setEnterpriseNo("2000002");

        DatasetValidateRes datasetValidateRes = datasetService.validateDataset(datasetSaveReq);

        DatasetValidateVO datasetValidateVO = new DatasetValidateVO();
        datasetValidateVO.setDatasetNo(validateDto.getParams().getDatasetNo());
        datasetValidateVO.setValid(datasetValidateRes.getValid());
        datasetValidateVO.setSql(datasetValidateRes.getSql());
        datasetValidateVO.setMessage(datasetValidateRes.getMessage());

        return datasetValidateVO;
    }
}