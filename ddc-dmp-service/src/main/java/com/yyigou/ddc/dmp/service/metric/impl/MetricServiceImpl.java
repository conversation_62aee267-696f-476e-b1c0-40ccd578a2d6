package com.yyigou.ddc.dmp.service.metric.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetMapper;
import com.yyigou.ddc.dmp.dao.metric.entity.Metric;
import com.yyigou.ddc.dmp.dao.metric.mapper.MetricMapper;
import com.yyigou.ddc.dmp.model.req.metric.*;
import com.yyigou.ddc.dmp.model.res.metric.MetricRes;
import com.yyigou.ddc.dmp.service.metric.MetricService;
import com.yyigou.ddc.dmp.service.util.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class MetricServiceImpl extends ServiceImpl<MetricMapper, Metric> implements MetricService {
    @Resource
    private DatasetMapper datasetMapper;

    @Resource
    private MetricMapper metricMapper;

    @Override
    @Transactional
    public String saveMetric(MetricSaveReq metricSaveReq) {
        Metric metric = BeanCopyUtil.copyFields(metricSaveReq, Metric.class);

        Dataset dataset = datasetMapper.selectOne(Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, metric.getEnterpriseNo())
                .eq(Dataset::getDatasetNo, metric.getDatasetNo())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataset) {
            throw new BusinessException("数据集不存在");
        }

        if (StringUtils.isEmpty(metric.getMetricNo())) {
            //新增
            metric.setMetricNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<Metric>lambdaQuery()
                    .eq(Metric::getEnterpriseNo, metric.getEnterpriseNo())
                    .eq(Metric::getMetricNo, metric.getMetricNo())
                    .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("指标不存在");
            }

            Metric toDeleteMetric = new Metric();
            toDeleteMetric.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteMetric, Wrappers.<Metric>lambdaQuery()
                    .eq(Metric::getEnterpriseNo, metric.getEnterpriseNo())
                    .eq(Metric::getMetricNo, metric.getMetricNo())
                    .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(metric);

        return metric.getMetricNo();
    }

    @Override
    public PageVo<MetricRes> queryListPage(MetricPageReq metricPageReq, PageDto pageDTO) {
        Page<Metric> page = PageHelper.startPage(pageDTO.getPageIndex(), pageDTO.getPageSize(), org.apache.commons.lang3.StringUtils.isBlank(pageDTO.getOrderBy()) ? "create_time desc" : pageDTO.getOrderBy());

        metricMapper.selectList(Wrappers.<Metric>lambdaQuery()
                .eq(Metric::getEnterpriseNo, metricPageReq.getEnterpriseNo())
                .in(Metric::getDatasetNo, metricPageReq.getDatasetNoList())
                .eq(metricPageReq.getStatus() != null, Metric::getStatus, metricPageReq.getStatus())
                .notIn(CollectionUtils.isNotEmpty(metricPageReq.getExcludeMetricNoList()), Metric::getMetricNo, metricPageReq.getExcludeMetricNoList())
                .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return PageUtils.convertPageVo(page,
                data -> wrapMetric(metricPageReq.getEnterpriseNo(), data)
        );
    }

    private List<MetricRes> wrapMetric(String enterpriseNo, List<Metric> metrics) {
        if (CollectionUtils.isEmpty(metrics)) {
            return Collections.emptyList();
        }
        return BeanCopyUtil.copyFieldsList(metrics, MetricRes.class);
    }

    @Override
    public List<MetricRes> getMetricList(MetricGetListReq metricGetListReq) {
        List<Metric> metrics = metricMapper.selectList(Wrappers.<Metric>lambdaQuery()
                .eq(Metric::getEnterpriseNo, metricGetListReq.getEnterpriseNo())
                .in(Metric::getMetricNo, metricGetListReq.getMetricNoList())
                .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return wrapMetric(metricGetListReq.getEnterpriseNo(), metrics);
    }

    @Override
    public List<MetricRes> getMetricByDatasetNo(MetricGetListByDatasetNoReq metricGetListByDatasetNoReq) {
        List<Metric> metrics = metricMapper.selectList(Wrappers.<Metric>lambdaQuery()
                .eq(Metric::getEnterpriseNo, metricGetListByDatasetNoReq.getEnterpriseNo())
                .eq(Metric::getDatasetNo, metricGetListByDatasetNoReq.getDatasetNo())
                .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isEmpty(metrics)) {
            return Collections.emptyList();
        }

        return wrapMetric(metricGetListByDatasetNoReq.getEnterpriseNo(), metrics);
    }

    @Override
    public MetricRes getMetric(MetricGetReq metricGetReq) {
        Metric metric = metricMapper.selectOne(Wrappers.<Metric>lambdaQuery()
                .eq(Metric::getEnterpriseNo, metricGetReq.getEnterpriseNo())
                .eq(Metric::getMetricNo, metricGetReq.getMetricNo())
                .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        List<MetricRes> metricRes = wrapMetric(metricGetReq.getEnterpriseNo(), Collections.singletonList(metric));
        return metricRes.get(0);
    }
}
