package com.yyigou.ddc.dmp.model.bo.sqlbuild;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/07/11
 */
@Data
@Builder
public class SqlFieldBO {

    /**
     * 模型字段编码
     */
    private String fieldCode;

    private String fieldAlias;

    /**
     * 模型编号
     */
    private String tableAlias;

//    /**
//     * 是否为聚合字段(用于扩展，聚合字段不用加入 group by)
//     */
//    private Boolean isAggregation;

    /**
     * 聚合类型
     */
    private String aggregationType;

}
