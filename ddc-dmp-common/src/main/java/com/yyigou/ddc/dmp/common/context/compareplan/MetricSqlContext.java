package com.yyigou.ddc.dmp.common.context.compareplan;

import com.yyigou.ddc.dmp.common.context.SqlExecuteContext;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.SqlFieldBO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/27
 */
@Data
@Builder
public class MetricSqlContext {

    private String datasetNo;

    private String joinType;

    private SqlExecuteContext sqlExecuteContext;

    private String cteName;

    /**
     * 比价方案外部查询字段列表
     */
    private List<SqlFieldBO> outSelectList;

}
