package com.yyigou.ddc.dmp.model.req.comparemodel;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefDatasetReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 数据集编号
     */
    @NotBlank(message = "关联数据集编号不能为空")
    private String datasetNo;

    /**
     关联类型 innerjoin,leftjoin,rightjoin
     */
    @NotNull(message = "数据集关联类型不能为空")
    private String joinType;

}
