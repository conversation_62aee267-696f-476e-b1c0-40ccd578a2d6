package com.yyigou.ddc.dmp.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.yyigou.ddc.dmp.common.constant.SqlConstant;
import com.yyigou.ddc.dmp.common.context.SqlExecuteContext;
import com.yyigou.ddc.dmp.common.enums.SqlAggTypeEnum;
import com.yyigou.ddc.dmp.common.enums.SqlJoinTypeEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.*;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.OrderByElement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SelectItem;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 动态构建 sql
 *
 * <AUTHOR>
 * @date 2025/07/11
 */
public class SqlBuilderUtil {

    /**
     * 根据上下文构建完整的 SQL 语句
     */
    public static String buildSelectSql(SqlExecuteContext context) throws JSQLParserException {
        validateSqlExecuteContext(context);

        PlainSelect plainSelect = new PlainSelect();
        // 设置主表
        Table mainTable = getTable(context.getMainTable().getCatalogName(), context.getMainTable().getSchemaName(), context.getMainTable().getTableName(), context.getMainTable().getTableAlias());
        plainSelect.setFromItem(mainTable);

        appendJoinToSelect(context, plainSelect);

        appendSelectItemsToSelect(context, plainSelect);

        appendWhereToSelect(context, plainSelect);

        appendGroupByToSelect(context, plainSelect);

        appendOrderByToSelect(context, plainSelect);

        return plainSelect.toString();
    }

    private static void appendOrderByToSelect(SqlExecuteContext context, PlainSelect plainSelect) {
        // 设置 ORDER BY
        if (CollectionUtil.isNotEmpty(context.getOrderByList())) {
            List<OrderByElement> orderByElements = new ArrayList<>();
            for (SqlOrderByBO orderByBO : context.getOrderByList()) {
                OrderByElement element = new OrderByElement();
                Column column = new Column(getTableFromTableAlias(orderByBO.getTableAlias()), StrUtil.isEmpty(orderByBO.getFieldAlias()) ? orderByBO.getFieldCode() : orderByBO.getFieldAlias());
                element.setExpression(column);
                element.setAsc(Objects.equals(orderByBO.getAsc(), Boolean.TRUE));
                orderByElements.add(element);
            }
            plainSelect.setOrderByElements(orderByElements);
        }
    }

    private static Table getTableFromTableAlias(String tableAlias) {
        Table table = new Table();
        table.setAlias(new Alias(tableAlias));
        return table;

    }

    private static Table getTable(String catalogName, String schemaName, String tableName, String alias) {
        Table table = null;
        if (StrUtil.isNotEmpty(catalogName)) {
            table = new Table(catalogName, schemaName, tableName);
        } else {
            table = new Table(schemaName, tableName);
        }
        if (StrUtil.isNotEmpty(alias)) {
            table.setAlias(new Alias(alias));
        }
        return table;
    }

    private static void appendGroupByToSelect(SqlExecuteContext context, PlainSelect plainSelect) {
        // 设置 GROUP BY
        if (CollectionUtil.isNotEmpty(context.getGroupByList())) {
            for (SqlFieldBO field : context.getGroupByList()) {
                Column column = new Column();
                column.setTable(getTableFromTableAlias(field.getTableAlias()));
                column.setColumnName(field.getFieldCode());
                plainSelect.addGroupByColumnReference(column);
            }
        }
    }

    /**
     * 当前的代码逻辑是基于 JSQLParser 构建表达式树，因此本质上已经规避了部分 SQL 注入问题，因为所有的值都会被封装为表达式对象（如 StringValue, LongValue），不会直接参与字符串拼接。
     *
     * @param context
     * @param plainSelect
     */
    private static void appendWhereToSelect(SqlExecuteContext context, PlainSelect plainSelect) {
        // 设置 WHERE 条件
        if (CollectionUtil.isNotEmpty(context.getWhereList())) {
            Expression whereExpr = null;
            for (SqlWhereBO condition : context.getWhereList()) {
                Table table = getTableFromTableAlias(condition.getTableAlias());
                Column column = new Column(table, condition.getFieldCode());
                // 统一转为大写
                String op = condition.getOperator().toUpperCase();
                Expression rightExpr;

                switch (op) {
                    case "=":
                        // 改用构造方法
                        rightExpr = createValueExpression(condition.getValue());
                        EqualsTo eq = new EqualsTo(column, rightExpr);
                        whereExpr = combineExpressions(whereExpr, eq);
                        break;
                    case ">":
                        // 改用构造方法
                        rightExpr = createValueExpression(condition.getValue());
                        GreaterThan gt = new GreaterThan(column, rightExpr);
                        whereExpr = combineExpressions(whereExpr, gt);
                        break;
                    case "<":
                        // 修正类名拼写
                        rightExpr = createValueExpression(condition.getValue());
                        MinorThan lt = new MinorThan(column, rightExpr);
                        whereExpr = combineExpressions(whereExpr, lt);
                        break;
                    case "LIKE":
                        // 添加通配符
                        rightExpr = new StringValue("%" + condition.getValue() + "%");
                        LikeExpression like = new LikeExpression();
                        like.setLeftExpression(column);
                        like.setRightExpression(rightExpr);
                        whereExpr = combineExpressions(whereExpr, like);
                        break;
                    case "IN":
                        // 转换为表达式列表
                        List<Expression> values = condition.getValueList().stream()
                                .map(SqlBuilderUtil::createValueExpression)
                                .collect(Collectors.toList());

                        // 创建 IN 表达式
                        InExpression in = new InExpression();
                        in.setLeftExpression(column);

                        // 设置 IN 后面的内容（必须用 ExpressionList 包裹）
                        ExpressionList<Expression> expressionList = new ExpressionList<>();
                        expressionList.addExpressions(values);
                        ParenthesedExpressionList<ExpressionList<Expression>> parenthesedList = new ParenthesedExpressionList<>();
                        parenthesedList.addExpression(expressionList);
                        in.setRightExpression(parenthesedList);
                        whereExpr = combineExpressions(whereExpr, in);
                        break;
                    case "IS NULL":
                        IsNullExpression isNull = new IsNullExpression();
                        isNull.setLeftExpression(column);
                        whereExpr = combineExpressions(whereExpr, isNull);
                        break;
                    // 添加更多操作符支持
                    case "!=":
                        rightExpr = createValueExpression(condition.getValue());
                        NotEqualsTo neq = new NotEqualsTo(column, rightExpr);
                        whereExpr = combineExpressions(whereExpr, neq);
                        break;
                    default:
                        throw new IllegalArgumentException("不支持的操作符: " + op);
                }
            }
            plainSelect.setWhere(whereExpr);
        }
    }

    private static void appendSelectItemsToSelect(SqlExecuteContext context, PlainSelect plainSelect) {
        // 设置 SELECT 字段
        List<SelectItem<?>> selectItems = new ArrayList<>();
        for (SqlFieldBO field : context.getSelectList()) {
            Table table = getTableFromTableAlias(field.getTableAlias());
            Column column = new Column(table, field.getFieldCode());

            SelectItem<?> selectItem = null;
            // 创建列对象（绑定表）
            if (field.getAggregationType() != null) {
                SqlAggTypeEnum aggType = SqlAggTypeEnum.getByJoinType(field.getAggregationType());
                if (aggType == null) {
                    throw new BusinessException("聚合类型【"+ field.getAggregationType()+"】不存在");
                }
                Function aggrFunction = new Function();
                aggrFunction.setName(aggType.getAggType());
                aggrFunction.setParameters(column);

                selectItem = new SelectItem<>(aggrFunction);
            } else {
                selectItem = new SelectItem<>(column);
            }

            // 设置字段别名
            if (StrUtil.isNotBlank(field.getFieldAlias())) {
                selectItem.setAlias(new Alias(field.getFieldAlias()));
            }

            selectItems.add(selectItem);
        }
        plainSelect.setSelectItems(selectItems);
    }

    private static void appendJoinToSelect(SqlExecuteContext context, PlainSelect plainSelect) throws JSQLParserException {
        // 添加 JOIN 表
        if (CollectionUtil.isNotEmpty(context.getJoinList())) {
            for (SqlJoinBO join : context.getJoinList()) {
                Join joinClause = new Join();
                Table joinTable = getTable(join.getCatalogName(), join.getSchemaName(), join.getTableName(), join.getTableAlias());
                joinClause.setRightItem(joinTable);

                List<SqlConditionOnBO> conditions = join.getJoinConditionList();
                String onCondition = conditions.stream()
                        .map(cond -> StrUtil.format(SqlConstant.SQL_JOIN_ON_FIELD_TO_FIELD,
                                cond.getLeftTableName(), cond.getLeftFieldCode(),
                                "=",
                                cond.getRightTableName(), cond.getRightFieldCode()))
                        .collect(Collectors.joining(" AND "));

                // 解析 ON 条件
                Expression onExpression = CCJSqlParserUtil.parseCondExpression(onCondition);
                joinClause.addOnExpression(onExpression);

                SqlJoinTypeEnum joinType = SqlJoinTypeEnum.getByJoinType(join.getJoinType());
                if (joinType == null) {
                    throw new BusinessException("不支持的 JOIN 类型: " + join.getJoinType());
                }
                // 设置 JOIN 类型
                switch (joinType) {
                    case SqlJoinTypeEnum.INNER_JOIN:
                        joinClause.setInner(true);
                        break;
                    case SqlJoinTypeEnum.LEFT_JOIN:
                        joinClause.setLeft(true);
                        break;
                    case SqlJoinTypeEnum.RIGHT_JOIN:
                        joinClause.setRight(true);
                        break;
                    default:
                        throw new IllegalArgumentException("不支持的 JOIN 类型: " + join.getJoinType());
                }

                plainSelect.addJoins(Collections.singletonList(joinClause));
            }
        }
    }


    /**
     * todo-zyc 校验sql执行上下文
     *
     * @param context
     */
    private static void validateSqlExecuteContext(SqlExecuteContext context) {
    }

    /**
     * 合并表达式 (提取公共逻辑)
     */
    private static Expression combineExpressions(Expression expr, Expression newExpr) {
        return (expr == null) ? newExpr : new AndExpression(expr, newExpr);
    }


    /**
     * 创建值表达式（自动识别类型）
     */
    private static Expression createValueExpression(Object value) {
        if (value instanceof String) {
            return new StringValue((String) value);
        } else if (value instanceof Number) {
            return new LongValue(value.toString());
        } else {
            return new StringValue(value.toString());
        }
    }


}
