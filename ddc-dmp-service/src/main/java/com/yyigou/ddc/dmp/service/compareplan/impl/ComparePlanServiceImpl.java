package com.yyigou.ddc.dmp.service.compareplan.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.constant.DmpConstant;
import com.yyigou.ddc.dmp.common.context.SqlExecuteContext;
import com.yyigou.ddc.dmp.common.context.compareplan.MetricSqlContext;
import com.yyigou.ddc.dmp.common.enums.*;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.SqlBuilderUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.mapper.ComparePlanMapper;
import com.yyigou.ddc.dmp.manager.integration.log.BusinessLogService;
import com.yyigou.ddc.dmp.model.bo.comparemodel.ModelDatasetBO;
import com.yyigou.ddc.dmp.model.bo.comparemodel.ModelDatasetFieldBO;
import com.yyigou.ddc.dmp.model.bo.comparemodel.ModelDatasetJoinBO;
import com.yyigou.ddc.dmp.model.bo.comparemodel.ModelMetricBO;
import com.yyigou.ddc.dmp.model.bo.compareplan.CompareModelFullDetailBO;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.*;
import com.yyigou.ddc.dmp.model.req.compareplan.*;
import com.yyigou.ddc.dmp.model.res.comparemodel.*;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanPageRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefFieldRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefMetricRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelCalMetricService;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelDimService;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelMetricService;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanCalculatedMetricService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanFieldService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanMetricService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanService;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Service
@Slf4j
public class ComparePlanServiceImpl extends DmpServiceImpl<ComparePlanMapper, ComparePlan> implements ComparePlanService {


    @Resource
    private ComparePlanFieldService comparePlanFieldService;

    @Resource
    private ComparePlanMetricService comparePlanMetricService;

    @Resource
    private ComparePlanCalculatedMetricService comparePlanCalculatedMetricService;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private CompareModelMetricService compareModelMetricService;

    @Resource
    private CompareModelService compareModelService;

    @Resource
    private CompareModelDimService compareModelDimService;

    @Resource
    private CompareModelCalMetricService compareModelCalMetricService;


    /**
     * 保存新增的模型
     *
     * @param comparePlanSaveReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ComparePlanPageRes save(ComparePlanSaveReq comparePlanSaveReq) {
        validateSavePlan(comparePlanSaveReq);
        ComparePlan comparePlan = BeanCopyUtil.copyFields(comparePlanSaveReq, ComparePlan.class);
        comparePlan.setPlanNo(IdUtil.objectId());
        this.save(comparePlan);

        comparePlanMetricService.savePlanRefMetric(comparePlanSaveReq, comparePlan, null);
        comparePlanCalculatedMetricService.savePlanRefCalculatedMetric(comparePlanSaveReq, comparePlan, null);
        comparePlanFieldService.savePlanRefField(comparePlanSaveReq, comparePlan);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_PLAN_VIEW_NO, comparePlan.getPlanNo(), "新增比价方案", "新增比价方案", "", "");
            }
        });
        return BeanCopyUtil.copyFields(comparePlan, ComparePlanPageRes.class);
    }

    /**
     * 校验比价方案
     * 只校验到比对模型层，不继续往下校验数据集、指标等
     *
     * @param comparePlanSaveReq
     */
    private void validateSavePlan(ComparePlanSaveReq comparePlanSaveReq) {
        ValidatorUtil.validateParams(comparePlanSaveReq);
        // todo-zyc 查询条件后续处理

        if (comparePlanSaveReq.getCompareMetricList().stream().noneMatch(e -> Objects.equals(e.getSelected(), true))) {
            throw new BusinessException("请选择至少一个比价指标");
        }

        // 校验编码和名称是否唯一
        checkModelCodeAndNameUnique(
                comparePlanSaveReq.getEnterpriseNo(),
                comparePlanSaveReq.getPlanCode(),
                comparePlanSaveReq.getPlanName(),
                comparePlanSaveReq.getPlanNo()
        );

        if (comparePlanSaveReq.getEnableStatus() == null) {
            comparePlanSaveReq.setEnableStatus(EnableStatusEnum.ENABLE.getValue());
        } else {
            EnableStatusEnum byValue = EnableStatusEnum.getByValue(comparePlanSaveReq.getEnableStatus());
            if (byValue == null) {
                throw new BusinessException("启用状态不正确");
            }
        }

        checkPlanRefField(comparePlanSaveReq.getRowList());
        checkPlanRefField(comparePlanSaveReq.getColumnList());
        checkPlanRefField(comparePlanSaveReq.getValueList());

        // 校验比对模型是否存在
        compareModelService.checkModelExist(comparePlanSaveReq.getCompareModelNo());
        // 校验模型指标是否存在
        compareModelMetricService.checkModelMetricExist(comparePlanSaveReq);
        // 校验模型字段是否存在
        compareModelDimService.checkModelDimExist(comparePlanSaveReq);
        //  校验计算指标是否存在
        compareModelCalMetricService.checkModelCalMetricExist(comparePlanSaveReq);


    }

    private void checkPlanRefField(List<ComparePlanRefFieldReq> fieldList) {
        if (CollectionUtils.isNotEmpty(fieldList)) {
            for (ComparePlanRefFieldReq fieldReq : fieldList) {
                if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_OBJECT.getValue())) {
                    if (fieldReq.getCompareModelDimNo() == null) {
                        throw new BusinessException("方案引用字段编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_DIM.getValue())) {
                    if (fieldReq.getCompareModelDimNo() == null) {
                        throw new BusinessException("方案引用字段编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_METRIC.getValue())) {
                    if (fieldReq.getComparePlanMetricNo() == null) {
                        throw new BusinessException("方案引用比对指标编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.BASELINE_METRIC.getValue())) {
                    if (fieldReq.getComparePlanMetricNo() == null) {
                        throw new BusinessException("方案引用基准指标编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.CALCULATED_METRIC.getValue())) {
                    if (fieldReq.getComparePlanCalMetricNo() == null) {
                        throw new BusinessException("方案引用计算指标编号不能为空");
                    }
                } else {
                    throw new BusinessException("方案引用字段来源类型不支持");
                }

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(ComparePlanSaveReq comparePlanSaveReq) {
        validateSavePlan(comparePlanSaveReq);
        ValidatorUtil.checkEmptyThrowEx(comparePlanSaveReq.getPlanNo(), "方案编号不能为空");
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, comparePlanSaveReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, comparePlanSaveReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");
        BeanUtils.copyProperties(comparePlanSaveReq, comparePlan);
        this.updateById(comparePlan);
        comparePlanMetricService.updatePlanRefMetric(comparePlanSaveReq, comparePlan);
        comparePlanCalculatedMetricService.updatePlanRefCalculatedMetric(comparePlanSaveReq, comparePlan);
        comparePlanFieldService.updatePlanRefField(comparePlanSaveReq, comparePlan);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_PLAN_VIEW_NO, comparePlanSaveReq.getPlanNo(), "修改比价方案", "修改比价方案", "", "");
            }
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean delete(ComparePlanGetReq deleteReq) {
        // 参数校验
        ValidatorUtil.validateParams(deleteReq);
        // 查询要删除的模型
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, deleteReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, deleteReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");

        // 逻辑删除模型
        comparePlan.setDeleted(DeletedEnum.DELETED.getValue());
        return this.updateById(comparePlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(ComparePlanChangeStatusReq changeStatusReq) {
        // 参数校验
        ValidatorUtil.validateParams(changeStatusReq);
        // 校验启用状态值是否合法
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.getByValue(changeStatusReq.getEnableStatus());
        String log = "停用比价方案";
        if (enableStatusEnum == null) {
            throw new BusinessException("启用状态值不正确");
        } else if (enableStatusEnum == EnableStatusEnum.ENABLE) {
            log = "启用比价方案";
        }
        String finalLog = log;

        // 查询要更新状态的模型
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, changeStatusReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, changeStatusReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");

        // 更新状态
        comparePlan.setEnableStatus(changeStatusReq.getEnableStatus());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_PLAN_VIEW_NO, comparePlan.getPlanNo(), finalLog, finalLog, "", "");
            }
        });

        return this.updateById(comparePlan);
    }

    @Override
    public ComparePlanGetRes get(ComparePlanGetReq getReq) {
        // 参数校验
        ValidatorUtil.validateParams(getReq);
        // 查询模型主信息
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, getReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, getReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");

        // 转换为返回结果
        ComparePlanGetRes comparePlanGetRes = BeanCopyUtil.copyFields(comparePlan, ComparePlanGetRes.class);

        // 填充关联信息
        fillRelatedData(comparePlanGetRes, comparePlan);

        return comparePlanGetRes;
    }

    private void fillRelatedData(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan) {

        comparePlanMetricService.fillMetricList(comparePlanGetRes, comparePlan);
        comparePlanCalculatedMetricService.fillCalculatedMetricList(comparePlanGetRes, comparePlan);
        comparePlanFieldService.fillFieldList(comparePlanGetRes, comparePlan);
    }

    @Override
    public PageVo<ComparePlanPageRes> pageQuery(ComparePlanPageQueryReq pageReq) {
        // 参数校验
        ValidatorUtil.checkEmptyThrowEx(pageReq, "分页查询参数不能为空");
        ValidatorUtil.checkEmptyThrowEx(pageReq.getEnterpriseNo(), "企业编号不能为空");

        // 设置默认排序
        String orderBy = pageReq.getOrderBy();
        if (StrUtil.isEmpty(orderBy)) {
            orderBy = "id DESC";
        }

        // 使用PageHelper进行分页
        try (Page<ComparePlan> pageInfo = PageHelper.startPage(pageReq.getPageIndex(), pageReq.getPageSize(), orderBy)) {
            // 构建查询条件
            LambdaQueryWrapper<ComparePlan> queryWrapper = Wrappers.lambdaQuery(ComparePlan.class)
                    .eq(ComparePlan::getEnterpriseNo, pageReq.getEnterpriseNo())
                    .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .like(StrUtil.isNotEmpty(pageReq.getPlanCode()), ComparePlan::getPlanCode, pageReq.getPlanCode())
                    .like(StrUtil.isNotEmpty(pageReq.getPlanName()), ComparePlan::getPlanName, pageReq.getPlanName())
                    .eq(pageReq.getEnableStatus() != null, ComparePlan::getEnableStatus, pageReq.getEnableStatus());

            // 执行查询
            List<ComparePlan> comparePlans = this.list(queryWrapper);

            // 转换为返回结果
            List<ComparePlanPageRes> pageResList = BeanCopyUtil.copyFieldsList(comparePlans, ComparePlanPageRes.class);

            // 返回分页结果
            return new PageVo<>(
                    pageInfo.getPageNum(),
                    pageInfo.getPageSize(),
                    pageInfo.getTotal(),
                    pageResList
            );
        }
    }

    @Override
    public ComparePlanGetRes getModelConfig(ComparePlanModelConfigReq getReq) {
        // 参数校验
        ValidatorUtil.validateParams(getReq);
        compareModelService.checkModelExist(getReq.getCompareModelNo());

        ComparePlanGetRes comparePlanGetRes = new ComparePlanGetRes();
        comparePlanGetRes.setCompareModelNo(getReq.getCompareModelNo());
        comparePlanGetRes.setEnterpriseNo(getReq.getEnterpriseNo());

        ComparePlan virtualComparePlan = new ComparePlan();
        virtualComparePlan.setEnterpriseNo(getReq.getEnterpriseNo());
        virtualComparePlan.setCompareModelNo(getReq.getCompareModelNo());
        virtualComparePlan.setPlanNo("");

//        // 填充指标、计算指标、比对对象、比对维度
//        comparePlanMetricService.fillMetricList(comparePlanGetRes, virtualComparePlan);
//        comparePlanCalculatedMetricService.fillCalculatedMetricList(comparePlanGetRes, virtualComparePlan);
        fillRelatedData(comparePlanGetRes, virtualComparePlan);
        return comparePlanGetRes;
    }

    /**
     * 执行比价方案的查询
     *
     * @param queryReq
     * @return
     */
    @Override
    public List<Map<String, Object>> queryData(ComparePlanQueryDataReq queryReq) {
        ComparePlanGetRes comparePlanGetRes = this.get(BeanCopyUtil.copyFields(queryReq, ComparePlanGetReq.class));

        /**
         * 第一步：查询出后续可能用到的数据
         * 1. 根据比价方案的模型编号查询比对模型
         * 2. 查询比对模型关联的比对数据集和基准数据集
         * 3. 查询比对模型关联的比对指标、基准指标、计算指标
         * 4. 查询比对模型的比对维度、比对对象的在数据集中的详细定义（所在库、所在表）
         *
         * 第二步：首先生成比对指标的查询sql
         * 1. 校验比价方案的值维度是否有启用的比对指标，没有则报错
         * 2. 根据比对指标的定义结合数据信息，生成聚合查询的select部分
         * 3. 校验比价方案的行维度是否存在，不存在则报错
         * 4. 根据比价方案的行列维度生成group by部分和select部分
         * 5. where条件部分强制加入租户字段过滤
         * 6. 将查询封装成 doris 的 with 语法
         *
         * 第三步：循环处理比价方案值维度中的基准指标，生成sql
         * 1. 根据基准指标的定义结合数据信息，生成聚合查询的select部分
         * 2. 根据比对模型的比对对象字段生成group by部分和select部分（基准指标和比对指标的统计粒度可能不一样）
         * 3. where条件部分强制加入租户字段过滤
         * 4. 将查询封装成 doris 的 with 语法
         *
         * 第四步：整合前面生成的多个查询sql的结果集（cte公用表达式，逻辑结果集），生成最终的sql
         * 1. 如果存在多个指标，则将多个结果集 join ，join方式由比对模型定义，join字段是比对模型的比对对象字段
         * 2. select 行列字段+基准指标结果+比对指标结果
         */

        // 第一步
        CompareModelFullDetailBO compareModelDetail = compareModelService.getCompareModelDetail(comparePlanGetRes.getCompareModelNo());
        Map<String, ModelMetricBO> metricMap = compareModelDetail.getMetricMap();
        Map<String, ModelDatasetBO> datasetMap = compareModelDetail.getDatasetMap();

        try {
            TableAliasAllocator aliasAllocator = new TableAliasAllocator();

            // 第二步
            MetricSqlContext compareMetricSqlContext = getCompareMetricSql(comparePlanGetRes, metricMap, datasetMap, compareModelDetail, aliasAllocator);
            // 第三步
            List<MetricSqlContext> baselineMetricSqlContext = getBaselineMetricSqlList(comparePlanGetRes, metricMap, datasetMap, compareModelDetail, aliasAllocator);
            // todo-zyc 计算指标

            // 第四步 封装最终的sql
            StringBuilder finalSqlStringBuilder = buildPlanSelectSql(compareMetricSqlContext, aliasAllocator, baselineMetricSqlContext, compareModelDetail);
            log.info("最终的sql：\n{}", finalSqlStringBuilder.toString());

        } catch (JSQLParserException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("SQL解析异常");
        }


        return List.of();
    }

    /**
     * 构建比价方案的查询sql
     *
     * @param compareMetricSqlContext
     * @param aliasAllocator
     * @param baselineMetricSqlContext
     * @param compareModelDetail
     * @return
     * @throws JSQLParserException
     */
    private StringBuilder buildPlanSelectSql(MetricSqlContext compareMetricSqlContext, TableAliasAllocator aliasAllocator, List<MetricSqlContext> baselineMetricSqlContext, CompareModelFullDetailBO compareModelDetail) throws JSQLParserException {
        StringBuilder finalSqlStringBuilder = new StringBuilder();

        // 先处理cte部分
        finalSqlStringBuilder.append("with \n");
        List<String> cteSqlList = new ArrayList<>();
        compareMetricSqlContext.setCteName(aliasAllocator.getNextCte());
        cteSqlList.add(" " + compareMetricSqlContext.getCteName() + " as ( " + SqlBuilderUtil.buildSelectSql(compareMetricSqlContext.getSqlExecuteContext()) + " )");
        for (MetricSqlContext baselineSqlItem : baselineMetricSqlContext) {
            baselineSqlItem.setCteName(aliasAllocator.getNextCte());
            cteSqlList.add(" " + baselineSqlItem.getCteName() + " as ( " + SqlBuilderUtil.buildSelectSql(baselineSqlItem.getSqlExecuteContext()) + " )");
        }
        finalSqlStringBuilder.append(String.join(",\n", cteSqlList));
        finalSqlStringBuilder.append("\n");

        // 然后将比对指标和基准指标的查询结果join起来，输出一个结果
        SqlExecuteContext finalSelectContext = new SqlExecuteContext();
        List<SqlFieldBO> selectList = new ArrayList<>();
        finalSelectContext.setMainTable(SqlTableBO.builder().tableName(compareMetricSqlContext.getCteName()).build());
        if (CollectionUtils.isNotEmpty(baselineMetricSqlContext)) {
            List<SqlJoinBO> joinList = new ArrayList<>();
            for (MetricSqlContext baselineSqlItem : baselineMetricSqlContext) {
                // 指标之间的 join 字段固定是比对对象字段
                List<SqlConditionOnBO> joinConditionList = new ArrayList<>();
                for (CompareModelRefFieldRes compareModelRefFieldRes : compareModelDetail.getCompareObjectList()) {
                    joinConditionList.add(SqlConditionOnBO.builder()
                            .leftFieldCode(compareModelRefFieldRes.getFieldCode())
                            .leftTableName(compareMetricSqlContext.getCteName())
                            .rightFieldCode(compareModelRefFieldRes.getFieldCode())
                            .rightTableName(baselineSqlItem.getCteName())
                            .build());
                }
                joinList.add(SqlJoinBO.builder().tableName(baselineSqlItem.getCteName()).joinType(baselineSqlItem.getJoinType()).joinConditionList(joinConditionList).build());
                for (SqlFieldBO sqlFieldBO : baselineSqlItem.getOutSelectList()) {
                    selectList.add(SqlFieldBO.builder().tableAlias(baselineSqlItem.getCteName()).fieldCode(sqlFieldBO.getFieldCode()).build());
                }
            }
            finalSelectContext.setJoinList(joinList);
        }

        for (SqlFieldBO fieldBO : compareMetricSqlContext.getOutSelectList()) {
            selectList.add(SqlFieldBO.builder().tableAlias(compareMetricSqlContext.getCteName()).fieldCode(fieldBO.getFieldCode()).fieldAlias(fieldBO.getFieldAlias()).build());
        }
        finalSelectContext.setSelectList(selectList);
        finalSqlStringBuilder.append(SqlBuilderUtil.buildSelectSql(finalSelectContext));
        return finalSqlStringBuilder;
    }


    /**
     * 获取比价指标的查询sql
     *
     * @param comparePlan
     * @param metricMap      key是compareModelMetricNo
     * @param datasetMap
     * @param compareModel
     * @param aliasAllocator
     */
    private MetricSqlContext getCompareMetricSql(ComparePlanGetRes comparePlan, Map<String, ModelMetricBO> metricMap, Map<String, ModelDatasetBO> datasetMap, CompareModelFullDetailBO compareModel, TableAliasAllocator aliasAllocator) throws JSQLParserException {
        Map<Integer, List<ComparePlanRefFieldRes>> valueMetricTypeMap = comparePlan.getValueList().stream().collect(Collectors.groupingBy(ComparePlanRefFieldRes::getSourceType));
        List<ComparePlanRefFieldRes> compareMetricList = valueMetricTypeMap.get(ComparePlanFieldSourceTypeEnum.COMPARE_METRIC.getValue());
        // 目前一个比价方案有且仅有一个比价指标，其实能做到一个指标有多个同类型（是否聚合）的比价指标
        if (CollectionUtils.isEmpty(compareMetricList)) {
            throw new BusinessException("请先配置一个比对指标");
        } else if (compareMetricList.size() > 1) {
            throw new BusinessException("只能配置一个比对指标");
        }
        ComparePlanRefFieldRes compareMetric = compareMetricList.getFirst();


        Map<String, ComparePlanRefMetricRes> comparePlanMetricNoMap = comparePlan.getCompareMetricList().stream().collect(Collectors.toMap(ComparePlanRefMetricRes::getComparePlanMetricNo, Function.identity()));
        ComparePlanRefMetricRes planCompareMetric = comparePlanMetricNoMap.get(compareMetric.getComparePlanMetricNo());
        ValidatorUtil.checkEmptyThrowEx(planCompareMetric, "方案比对指标【" + compareMetric.getComparePlanMetricNo() + "】已失效，请重新配置比价方案");

        ModelMetricBO metricRes = metricMap.get(planCompareMetric.getCompareModelMetricNo());
        ValidatorUtil.checkEmptyThrowEx(metricRes, "模型比对指标【" + planCompareMetric.getCompareModelMetricNo() + "】已失效，请重新配置比价方案");

        // 比对对象字段永远作为group by字段
        SqlExecuteContext sqlExecuteContext = new SqlExecuteContext();
        List<SqlFieldBO> outSelectList = new ArrayList<>();
        ModelDatasetBO metricDataset = datasetMap.get(metricRes.getDatasetNo());
        ValidatorUtil.checkEmptyThrowEx(metricDataset, "数据集【" + metricRes.getDatasetNo() + "】已失效，请重新配置比价方案");

        // 设置主表
        setMainTable(metricDataset, aliasAllocator, sqlExecuteContext);
        // 处理 JOIN表
        setJoinTable(metricDataset, aliasAllocator, sqlExecuteContext);
        // 设置 SELECT
        setSelect(compareModel, sqlExecuteContext, aliasAllocator, metricDataset, metricRes, comparePlan, CompareModelMetricTypeEnum.COMPARE_METRIC, outSelectList);
        // 设置 GROUP BY
        setGroupBy(metricDataset, compareModel, aliasAllocator, sqlExecuteContext, comparePlan, CompareModelMetricTypeEnum.COMPARE_METRIC);
        // 设置 WHERE
        setWhere(aliasAllocator, metricDataset, sqlExecuteContext);

        return MetricSqlContext.builder().sqlExecuteContext(sqlExecuteContext)
                .datasetNo(metricDataset.getDatasetNo())
                .outSelectList(outSelectList)
                .build();
    }

    /**
     * 获取多个基准指标的查询sql，目前每个基准指标对应一个sql（后续优化同一个数据集的同一类型用一条sql）
     *
     * @param comparePlan
     * @param metricMap
     * @param datasetMap
     * @param compareModel
     * @param aliasAllocator
     * @return
     */
    private List<MetricSqlContext> getBaselineMetricSqlList(ComparePlanGetRes comparePlan, Map<String, ModelMetricBO> metricMap, Map<String, ModelDatasetBO> datasetMap, CompareModelFullDetailBO compareModel, TableAliasAllocator aliasAllocator) throws JSQLParserException {
        List<MetricSqlContext> baselineSql = new ArrayList<>();

        Map<String, CompareModelRefDatasetRes> baselineDatasetMap = compareModel.getBaselineDatasetList().stream().collect(Collectors.toMap(CompareModelRefDatasetRes::getDatasetNo, Function.identity()));

        List<ComparePlanRefFieldRes> valueList = comparePlan.getValueList();
        Map<String, ComparePlanRefMetricRes> planBaseLineMetricNoMap = comparePlan.getBaselineMetricList().stream().collect(Collectors.toMap(ComparePlanRefMetricRes::getComparePlanMetricNo, Function.identity()));
        for (ComparePlanRefFieldRes comparePlanRefFieldRes : valueList) {
            if (Objects.equals(ComparePlanFieldSourceTypeEnum.BASELINE_METRIC.getValue(), comparePlanRefFieldRes.getSourceType())) {
                ComparePlanRefMetricRes comparePlanRefMetricRes = planBaseLineMetricNoMap.get(comparePlanRefFieldRes.getComparePlanMetricNo());
                ValidatorUtil.checkEmptyThrowEx(comparePlanRefMetricRes, "方案基准指标【" + comparePlanRefFieldRes.getComparePlanMetricNo() + "】已失效，请重新配置比价方案");

                ModelMetricBO metricRes = metricMap.get(comparePlanRefMetricRes.getCompareModelMetricNo());
                ValidatorUtil.checkEmptyThrowEx(metricRes, "模型基准指标【" + comparePlanRefMetricRes.getCompareModelMetricNo() + "】已失效，请重新配置比价方案");

                ModelDatasetBO metricDataset = datasetMap.get(metricRes.getDatasetNo());
                ValidatorUtil.checkEmptyThrowEx(metricDataset, "数据集【" + metricRes.getDatasetNo() + "】已失效，请重新配置比价方案");
                SqlExecuteContext sqlExecuteContext = new SqlExecuteContext();

                List<SqlFieldBO> outSelectList = new ArrayList<>();
                // 设置主表
                setMainTable(metricDataset, aliasAllocator, sqlExecuteContext);
                // 处理 JOIN表
                setJoinTable(metricDataset, aliasAllocator, sqlExecuteContext);
                // 设置 SELECT
                setSelect(compareModel, sqlExecuteContext, aliasAllocator, metricDataset, metricRes, comparePlan, CompareModelMetricTypeEnum.BASELINE_METRIC, outSelectList);
                // 设置 GROUP BY
                setGroupBy(metricDataset, compareModel, aliasAllocator, sqlExecuteContext, comparePlan, CompareModelMetricTypeEnum.BASELINE_METRIC);
                // 设置 WHERE
                setWhere(aliasAllocator, metricDataset, sqlExecuteContext);

                String joinType = baselineDatasetMap.get(metricDataset.getDatasetNo()).getJoinType();

                baselineSql.add(MetricSqlContext.builder().datasetNo(metricDataset.getDatasetNo()).sqlExecuteContext(sqlExecuteContext).outSelectList(outSelectList).joinType(joinType).build());
            }
        }
        return baselineSql;
    }

    /**
     * 处理where条件
     * 1. 数据集中的所有表都默认带上租户过滤条件
     *
     * @param aliasAllocator
     * @param metricDataset
     * @param sqlExecuteContext
     */
    private void setWhere(TableAliasAllocator aliasAllocator, ModelDatasetBO metricDataset, SqlExecuteContext sqlExecuteContext) {
        // 数据集中的所有表都默认带上租户过滤条件
        List<SqlWhereBO> whereList = new ArrayList<>();

        String tenantNo = UserHandleUtils.getOperationModel().getTenantNo();

        String fieldTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), metricDataset.getMainTable().getCatalogName(), metricDataset.getMainTable().getSchemaName(), metricDataset.getMainTable().getTableName());
        SqlWhereBO enterpriseConditionOfMainTable = SqlWhereBO.builder()
                .fieldCode(DmpConstant.ENTERPRISE_NO)
                .tableAlias(fieldTableAlias)
                .operator(SqlOperatorEnum.EQ.getOperator())
                .value(tenantNo).build();
        whereList.add(enterpriseConditionOfMainTable);
        if (CollectionUtils.isNotEmpty(metricDataset.getJoinList())) {
            for (ModelDatasetJoinBO join : metricDataset.getJoinList()) {
                String joinTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), join.getCatalogName(), join.getSchemaName(), join.getTableName());
                SqlWhereBO enterpriseConditionOfJoinTable = SqlWhereBO.builder()
                        .fieldCode(DmpConstant.ENTERPRISE_NO)
                        .tableAlias(joinTableAlias)
                        .operator(SqlOperatorEnum.EQ.getOperator()).build();
                whereList.add(enterpriseConditionOfJoinTable);
            }
        }
        sqlExecuteContext.setWhereList(whereList);
    }

    /**
     * 对于非聚合指标，不需要 group by
     * 对于聚合指标
     * 1. 如果是比对指标：group by 内容是比对模型中的比对对象字段 + 比价方案的比对维度
     * 2. 如果是基准指标：group by 内容只是比对模型中的比对对象字段
     *
     * @param metricDataset
     * @param compareModel
     * @param aliasAllocator
     * @param sqlExecuteContext
     * @param comparePlan
     * @param baselineMetric
     */
    private void setGroupBy(ModelDatasetBO metricDataset, CompareModelFullDetailBO compareModel, TableAliasAllocator aliasAllocator, SqlExecuteContext sqlExecuteContext, ComparePlanGetRes comparePlan, CompareModelMetricTypeEnum baselineMetric) {
        // 1 处理比对对象
        List<SqlFieldBO> groupByList = new ArrayList<>();
        for (CompareModelRefFieldRes compareModelRefFieldRes : compareModel.getCompareObjectList()) {
            SqlFieldBO groupField = SqlFieldBO.builder().fieldCode(compareModelRefFieldRes.getFieldCode()).build();
            ModelDatasetFieldBO fieldRes = metricDataset.getFieldMap().get(compareModelRefFieldRes.getFieldCode());
            ValidatorUtil.checkEmptyThrowEx(fieldRes, "数据集【" + metricDataset.getDatasetCode() + "】的字段【" + compareModelRefFieldRes.getFieldCode() + "】已失效");

            String fieldTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), fieldRes.getCatalogName(), fieldRes.getSchemaName(), fieldRes.getTableName());
            groupField.setTableAlias(fieldTableAlias);
            groupByList.add(groupField);
        }

        // 2 处理比对维度
        if (baselineMetric == CompareModelMetricTypeEnum.COMPARE_METRIC) {
            List<ComparePlanRefFieldRes> rowOrColumnList = new ArrayList<>(comparePlan.getRowList());
            if (CollectionUtils.isNotEmpty(comparePlan.getColumnList())) {
                rowOrColumnList.addAll(comparePlan.getColumnList());
            }
            Map<String, CompareModelRefFieldRes> comparePlanDimMap = comparePlan.getCompareDimList().stream().collect(Collectors.toMap(CompareModelRefFieldRes::getCompareModelDimNo, Function.identity()));
            for (ComparePlanRefFieldRes comparePlanRefFieldRes : rowOrColumnList) {
                if (Objects.equals(comparePlanRefFieldRes.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_DIM.getValue())) {
                    // 只需要处理比对维度
                    CompareModelRefFieldRes comparePlanDim = comparePlanDimMap.get(comparePlanRefFieldRes.getCompareModelDimNo());
                    ModelDatasetFieldBO fieldRes = metricDataset.getFieldMap().get(comparePlanDim.getFieldCode());
                    ValidatorUtil.checkEmptyThrowEx(fieldRes, "数据集【" + metricDataset.getDatasetCode() + "】的字段【" + comparePlanDim.getFieldCode() + "】已失效");

                    String fieldTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), fieldRes.getCatalogName(), fieldRes.getSchemaName(), fieldRes.getTableName());
                    groupByList.add(SqlFieldBO.builder()
                            .fieldCode(fieldRes.getFieldCode())
                            .tableAlias(fieldTableAlias)
                            .build());
                }
            }
        }


        sqlExecuteContext.setGroupByList(groupByList);
    }

    /**
     * 生成select内容，有3部分
     * 1. 固定的比对对象字段
     * 2. 可选的比对维度字段
     * 3. 指标部分
     *
     * @param compareModel
     * @param sqlExecuteContext
     * @param aliasAllocator
     * @param metricDataset
     * @param metricBO
     * @param comparePlan
     * @param outSelectList
     */
    private static void setSelect(CompareModelFullDetailBO compareModel, SqlExecuteContext sqlExecuteContext, TableAliasAllocator aliasAllocator, ModelDatasetBO metricDataset, ModelMetricBO metricBO, ComparePlanGetRes comparePlan, CompareModelMetricTypeEnum metricType, List<SqlFieldBO> outSelectList) {
        List<SqlFieldBO> selectList = new ArrayList<>();
        sqlExecuteContext.setSelectList(selectList);
        // 1 先处理固定的比对对象字段
        for (CompareModelRefFieldRes compareModelRefFieldRes : compareModel.getCompareObjectList()) {
            ModelDatasetFieldBO fieldRes = metricDataset.getFieldMap().get(compareModelRefFieldRes.getFieldCode());
            ValidatorUtil.checkEmptyThrowEx(fieldRes, "数据集【" + metricDataset.getDatasetCode() + "】的字段【" + compareModelRefFieldRes.getFieldCode() + "】已失效");

            String fieldTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), fieldRes.getCatalogName(), fieldRes.getSchemaName(), fieldRes.getTableName());
            selectList.add(SqlFieldBO.builder()
                    .fieldCode(fieldRes.getFieldCode())
                    .tableAlias(fieldTableAlias)
                    .build());

            if (metricType == CompareModelMetricTypeEnum.COMPARE_METRIC) {
                outSelectList.add(SqlFieldBO.builder()
                        .fieldCode(fieldRes.getFieldCode())
                        .build());
            }
        }


        // 2 处理指标值段
        ModelDatasetFieldBO metricField = metricDataset.getFieldMap().get(metricBO.getFieldCode());
        ValidatorUtil.checkEmptyThrowEx(metricField, "数据集【" + metricDataset.getDatasetCode() + "】的字段【" + metricBO.getFieldCode() + "】已失效");
        String fieldTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), metricField.getCatalogName(), metricField.getSchemaName(), metricField.getTableName());
        selectList.add(SqlFieldBO.builder()
                .fieldCode(metricField.getFieldCode())
                .fieldAlias(metricBO.getMetricCode())
                .tableAlias(fieldTableAlias)
                // 指标可能有聚合
                .aggregationType(metricBO.getAggType())
                .build());

        outSelectList.add(SqlFieldBO.builder()
                .fieldCode(metricBO.getMetricCode())
                .build());

        Map<String, CompareModelRefFieldRes> comparePlanDimMap = comparePlan.getCompareDimList().stream().collect(Collectors.toMap(CompareModelRefFieldRes::getCompareModelDimNo, Function.identity()));

        // 3 处理可选的比对维度字段
        if (metricType == CompareModelMetricTypeEnum.COMPARE_METRIC) {
            List<ComparePlanRefFieldRes> rowOrColumnList = new ArrayList<>(comparePlan.getRowList());
            if (CollectionUtils.isNotEmpty(comparePlan.getColumnList())) {
                rowOrColumnList.addAll(comparePlan.getColumnList());
            }
            for (ComparePlanRefFieldRes comparePlanRefFieldRes : rowOrColumnList) {
                if (Objects.equals(comparePlanRefFieldRes.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_DIM.getValue())) {
                    // 只需要处理比对维度
                    CompareModelRefFieldRes comparePlanDim = comparePlanDimMap.get(comparePlanRefFieldRes.getCompareModelDimNo());
                    ModelDatasetFieldBO fieldRes = metricDataset.getFieldMap().get(comparePlanDim.getFieldCode());

                    ValidatorUtil.checkEmptyThrowEx(fieldRes, "数据集【" + metricDataset.getDatasetCode() + "】的字段【" + comparePlanDim.getFieldCode() + "】已失效");
                    fieldTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), fieldRes.getCatalogName(), fieldRes.getSchemaName(), fieldRes.getTableName());
                    selectList.add(SqlFieldBO.builder()
                            .fieldCode(fieldRes.getFieldCode())
                            .tableAlias(fieldTableAlias)
                            .build());
                }
            }
        }

    }

    private static void setJoinTable(ModelDatasetBO metricDataset, TableAliasAllocator aliasAllocator, SqlExecuteContext sqlExecuteContext) {
        if (CollectionUtils.isNotEmpty(metricDataset.getJoinList())) {
            List<SqlJoinBO> joinList = new ArrayList<>();
            for (ModelDatasetJoinBO join : metricDataset.getJoinList()) {
                String joinTableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), join.getCatalogName(), join.getSchemaName(), join.getTableName());
                join.setTableAlias(joinTableAlias);

                SqlJoinBO sqlJoinBO = SqlJoinBO.builder()
                        .joinType(join.getJoinType())
                        .tableAlias(join.getTableAlias())
                        .tableName(join.getTableName())
                        .catalogName(join.getCatalogName())
                        .schemaName(join.getSchemaName())
                        .joinCondition(join.getJoinCondition())
                        .build();
                joinList.add(sqlJoinBO);
            }
            sqlExecuteContext.setJoinList(joinList);
        }
    }

    private static void setMainTable(ModelDatasetBO metricDataset, TableAliasAllocator aliasAllocator, SqlExecuteContext sqlExecuteContext) {
        ModelDatasetJoinBO mainTable = metricDataset.getMainTable();
        String tableAlias = aliasAllocator.getTableAlias(metricDataset.getDatasetNo(), metricDataset.getMainTable().getCatalogName(), metricDataset.getMainTable().getSchemaName(), metricDataset.getMainTable().getTableName());
        mainTable.setTableAlias(tableAlias);
        sqlExecuteContext.setMainTable(SqlTableBO.builder()
                .tableName(mainTable.getTableName()).catalogName(mainTable.getCatalogName()).schemaName(mainTable.getSchemaName()).tableAlias(mainTable.getTableAlias()).build());
    }


    /**
     * 校验同一租户下方案编码和名称是否唯一
     *
     * @param enterpriseNo 租户编号
     * @param planCode     方案编码
     * @param planName     方案名称
     * @param planNo       方案编号（用于更新场景排除自身）
     */
    private void checkModelCodeAndNameUnique(String enterpriseNo, String planCode, String planName, String planNo) {
        // 校验模型编码是否唯一
        LambdaQueryWrapper<ComparePlan> codeQueryWrapper = Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getEnterpriseNo, enterpriseNo)
                .eq(ComparePlan::getPlanCode, planCode)
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue());

        // 如果是更新场景，排除自身
        if (StrUtil.isNotEmpty(planNo)) {
            codeQueryWrapper.ne(ComparePlan::getPlanNo, planNo);
        }

        if (this.count(codeQueryWrapper) > 0) {
            throw new BusinessException("方案编码已存在");
        }

        // 校验模型名称是否唯一
        LambdaQueryWrapper<ComparePlan> nameQueryWrapper = Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getEnterpriseNo, enterpriseNo)
                .eq(ComparePlan::getPlanName, planName)
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue());

        // 如果是更新场景，排除自身
        if (StrUtil.isNotEmpty(planNo)) {
            nameQueryWrapper.ne(ComparePlan::getPlanNo, planNo);
        }

        if (this.count(nameQueryWrapper) > 0) {
            throw new BusinessException("方案名称已存在");
        }
    }


}
