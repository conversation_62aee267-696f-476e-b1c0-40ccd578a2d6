package com.yyigou.ddc.dmp.model.bo.sqlbuild;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/11
 */
@Data
@Builder
public class SqlJoinBO {

    private String joinType;

    private String tableAlias;

    private String tableName;

    private String catalogName;

    private String schemaName;

    /**
     * join的sql
     */
    private String joinCondition;

    /**
     * join的结构化对象
     */
    private List<SqlConditionOnBO> joinConditionList;

}
