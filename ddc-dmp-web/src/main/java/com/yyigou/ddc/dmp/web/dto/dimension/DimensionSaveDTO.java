package com.yyigou.ddc.dmp.web.dto.dimension;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionFieldSaveReq;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DimensionSaveDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "维度编号")
    private String dimensionNo;

    @EntityField(name = "维度编码")
    private String dimensionCode;

    @EntityField(name = "维度名称")
    private String dimensionName;

    @EntityField(name = "目录名称")
    private String catalogName;

    @EntityField(name = "模式名称")
    private String schemaName;

    @EntityField(name = "表名称")
    private String tableName;

    @EntityField(name = "描述")
    private String description;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "是否删除")
    private Integer deleted;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "创建人名称")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改人编号")
    private String modifyNo;

    @EntityField(name = "修改人名称")
    private String modifyName;

    @EntityField(name = "修改时间")
    private String modifyTime;

    @EntityField(name = "最后操作时间")
    private LocalDateTime opTimestamp;

    @EntityField(name = "维度字段列表")
    private List<DimensionFieldSaveDTO> dimensionFields;
}
