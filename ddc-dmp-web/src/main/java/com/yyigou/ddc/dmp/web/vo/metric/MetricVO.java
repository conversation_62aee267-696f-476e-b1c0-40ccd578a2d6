package com.yyigou.ddc.dmp.web.vo.metric;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MetricVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "指标编号")
    private String metricNo;

    @EntityField(name = "指标编码")
    private String metricCode;

    @EntityField(name = "指标名称")
    private String metricName;

    @EntityField(name = "单位")
    private String unit;

    @EntityField(name = "数值精度")
    private String numPrecision;

    @EntityField(name = "显示格式")
    private String displayFormat;

    @EntityField(name = "描述")
    private String description;

    @EntityField(name = "数据集编号")
    private String datasetNo;

    @EntityField(name = "数据集字段编号")
    private String datasetFieldNo;

    @EntityField(name = "聚合类型")
    private String aggType;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "是否删除")
    private Integer deleted;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "创建人名称")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改人编号")
    private String modifyNo;

    @EntityField(name = "修改人名称")
    private String modifyName;

    @EntityField(name = "修改时间")
    private String modifyTime;

    @EntityField(name = "最后操作时间")
    private LocalDateTime opTimestamp;
}

