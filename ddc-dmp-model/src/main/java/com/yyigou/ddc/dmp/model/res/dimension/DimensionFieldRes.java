package com.yyigou.ddc.dmp.model.res.dimension;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class DimensionFieldRes implements Serializable {
    private Long id;
    private String enterpriseNo;
    private String dimensionNo;
    private String fieldCode;
    private String fieldSemantic;
    private String description;
    private Integer displayOrder;
    private String dateFormat;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}
