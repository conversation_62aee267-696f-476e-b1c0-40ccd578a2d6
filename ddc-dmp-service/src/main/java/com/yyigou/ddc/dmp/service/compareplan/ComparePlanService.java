package com.yyigou.ddc.dmp.service.compareplan;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.model.req.compareplan.*;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanPageRes;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
public interface ComparePlanService extends IService<ComparePlan> {

    ComparePlanPageRes save(ComparePlanSaveReq comparePlanSaveReq);

    Boolean update(ComparePlanSaveReq comparePlanSaveReq);

    Boolean delete(ComparePlanGetReq deleteReq);

    Boolean changeStatus(ComparePlanChangeStatusReq changeStatusReq);

    ComparePlanGetRes get(ComparePlanGetReq getReq);

    PageVo<ComparePlanPageRes> pageQuery(ComparePlanPageQueryReq pageReq);

    ComparePlanGetRes getModelConfig(ComparePlanModelConfigReq getReq);

    List<Map<String, Object>> queryData(ComparePlanQueryDataReq queryReq);
}
