package com.yyigou.ddc.dmp.web.dto.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
public class DatasetJoinRelSaveDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "租户编号")
    private String enterpriseNo;

    @EntityField(name = "数据集编号")
    private String datasetNo;

    @EntityField(name = "目标目录名称")
    private String targetCatalogName;

    @EntityField(name = "目标模式名称")
    private String targetSchemaName;

    @EntityField(name = "目标表名称")
    private String targetTableName;

    @EntityField(name = "连接类型")
    private String joinType;

    @EntityField(name = "连接条件")
    private String joinCondition;

    @EntityField(name = "状态")
    private Integer status;

    @EntityField(name = "是否删除")
    private Integer deleted;

    @EntityField(name = "创建人编号")
    private String createNo;

    @EntityField(name = "创建人名称")
    private String createName;

    @EntityField(name = "创建时间")
    private String createTime;

    @EntityField(name = "修改人编号")
    private String modifyNo;

    @EntityField(name = "修改人名称")
    private String modifyName;

    @EntityField(name = "修改时间")
    private String modifyTime;

    @EntityField(name = "最后操作时间")
    private LocalDateTime opTimestamp;
}

