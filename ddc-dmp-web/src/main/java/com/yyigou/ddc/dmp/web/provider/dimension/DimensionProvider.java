package com.yyigou.ddc.dmp.web.provider.dimension;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.req.dimension.*;
import com.yyigou.ddc.dmp.model.res.dimension.DimensionRes;
import com.yyigou.ddc.dmp.service.dimension.DimensionService;
import com.yyigou.ddc.dmp.web.api.dimension.DimensionAPI;
import com.yyigou.ddc.dmp.web.dto.dimension.*;
import com.yyigou.ddc.dmp.web.vo.dimension.DatasetFieldDimensionRefVO;
import com.yyigou.ddc.dmp.web.vo.dimension.DimensionVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Interface(name = "维度")
@Component("dimensionProvider")
public class DimensionProvider extends ServiceBaseAbstract implements DimensionAPI {
    @Resource
    private DimensionService dimensionService;

    @Override
    @Method(aliasName = "ddc.dmp.dimension.findListPage", name = "查询维度", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<PageVo<DimensionVO>> findListPage(DimensionPageDTO params, PageDto pageParams) {
        try {
            DimensionPageReq dimensionPageReq = BeanCopyUtil.copyFieldsByJson(params, DimensionPageReq.class);
//        dimensionQueryReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            dimensionPageReq.setEnterpriseNo("2000002");
            PageVo<DimensionRes> dimensionResPageVo = dimensionService.queryListPage(dimensionPageReq, pageParams);

            List<DimensionVO> dimensionVOS = BeanCopyUtil.copyFieldsList(dimensionResPageVo.getRows(), DimensionVO.class);
            PageVo<DimensionVO> pageVo = new PageVo<>(dimensionResPageVo.getPageIndex(), dimensionResPageVo.getPageSize(), dimensionResPageVo.getTotal(), dimensionVOS);
            return CallResult.success(pageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.dimension.save", name = "保存维度", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<DimensionVO> saveDimension(DimensionSaveDTO params) {
        try {
            DimensionSaveReq dimensionSaveReq = BeanCopyUtil.copyFieldsByJson(params, DimensionSaveReq.class);
//        dimensionSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            dimensionSaveReq.setEnterpriseNo("2000002");
            String dimensionNo = dimensionService.saveDimension(dimensionSaveReq);

            DimensionVO dimensionVO = new DimensionVO();
            dimensionVO.setDimensionNo(dimensionNo);
            return CallResult.success(dimensionVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.dimension.get", name = "查看维度", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DimensionVO> getDimension(DimensionGetDTO params) {
        try {
            DimensionGetReq dimensionGetReq = BeanCopyUtil.copyFieldsByJson(params, DimensionGetReq.class);
//        dimensionGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            dimensionGetReq.setEnterpriseNo("2000002");
            DimensionRes dimension = dimensionService.getDimension(dimensionGetReq);

            DimensionVO dimensionVO = BeanCopyUtil.copyFields(dimension, DimensionVO.class);
            return CallResult.success(dimensionVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.dimension.delete", name = "删除维度", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<Boolean> deleteDimension(DimensionDeleteDTO params) {
        try {
            DimensionDeleteReq dimensionDeleteReq = BeanCopyUtil.copyFieldsByJson(params, DimensionDeleteReq.class);
//        dimensionDeleteReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            dimensionDeleteReq.setEnterpriseNo("2000002");
            dimensionService.deleteDimension(dimensionDeleteReq);

            return CallResult.success(Boolean.TRUE);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.dimension.startStop", name = "启停维度", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<Boolean> startStop(DimensionStartStopDTO params) {
        try {
            DimensionStartStopReq dimensionStartStopReq = BeanCopyUtil.copyFieldsByJson(params, DimensionStartStopReq.class);
//        dimensionStartStopReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            dimensionStartStopReq.setEnterpriseNo("2000002");
            dimensionService.startStop(dimensionStartStopReq);

            return CallResult.success(Boolean.TRUE);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    @Override
    @Method(aliasName = "ddc.dmp.dimension.saveDatasetFieldDimensionRef", name = "关联数据集字段和维度", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<DatasetFieldDimensionRefVO> saveDatasetFieldDimensionRef(DatasetFieldDimensionRefSaveDTO params) {
        try {
            DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq = BeanCopyUtil.copyFieldsByJson(params, DatasetFieldDimensionRefSaveReq.class);
//        datasetFieldDimensionRefSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
            datasetFieldDimensionRefSaveReq.setEnterpriseNo("2000002");
            Long refId = dimensionService.saveDatasetFieldDimensionRef(datasetFieldDimensionRefSaveReq);

            DatasetFieldDimensionRefVO datasetFieldDimensionRefVO = new DatasetFieldDimensionRefVO();
            datasetFieldDimensionRefVO.setId(refId);
            return CallResult.success(datasetFieldDimensionRefVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}