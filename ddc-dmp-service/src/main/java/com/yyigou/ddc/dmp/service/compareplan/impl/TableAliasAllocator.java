package com.yyigou.ddc.dmp.service.compareplan.impl;

import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 表别名分配器
 * <AUTHOR>
 * @date 2025/08/26
 */

public class TableAliasAllocator {

    public static final String CACHE_KEY_FORMAT = "{}:{}:{}:{}";

    private int tableAliasIndex;

    private int cteIndex;

    public String getNextTableAlias() {
        return "alias_" + tableAliasIndex++;
    }

    private Map<String, String> aliasMap = new HashMap<>();

    public String getNextCte() {
        return "cte_" + cteIndex++;
    }


    public String getTableAlias(String datasetNo, String catalog, String database, String table) {
        if (StrUtil.isEmpty(catalog)) {
            catalog = "";
        }
        String cacheKey = StrUtil.format(CACHE_KEY_FORMAT, datasetNo, catalog, database, table);
        return aliasMap.computeIfAbsent(cacheKey, k -> getNextTableAlias());
    }

}
