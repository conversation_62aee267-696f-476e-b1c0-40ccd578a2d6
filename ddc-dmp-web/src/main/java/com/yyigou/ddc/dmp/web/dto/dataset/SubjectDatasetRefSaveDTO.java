package com.yyigou.ddc.dmp.web.dto.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SubjectDatasetRefSaveDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分析主题ID
     */
    @EntityField(name = "分析主题ID")
    private String subjectNo;

    /**
     * 数据集ID
     */
    @EntityField(name = "数据集ID")
    private String datasetNo;

}
