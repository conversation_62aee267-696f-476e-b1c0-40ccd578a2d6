package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.model.req.metric.MetricGetReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricPageReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricSaveReq;
import com.yyigou.ddc.dmp.model.res.metric.MetricRes;
import com.yyigou.ddc.dmp.service.metric.MetricService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricGetDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricPageDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricSaveDTO;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.metric.MetricVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 指标前端控制器
 */
@RestController
@RequestMapping("/metric")
public class MetricController {
    @Resource
    private MetricService metricService;

    /**
     * 保存指标
     */
    @PostMapping("/save")
    public MetricVO save(@RequestBody CommonDTO<MetricSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        MetricSaveReq metricSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), MetricSaveReq.class);
//        metricSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        metricSaveReq.setEnterpriseNo("2000002");
        String metricNo = metricService.saveMetric(metricSaveReq);

        MetricVO metricVO = new MetricVO();
        metricVO.setMetricNo(metricNo);
        return metricVO;
    }

    /**
     * 查询指标
     */
    @GetMapping("/get")
    public MetricVO get(@RequestBody CommonDTO<MetricGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);

        MetricGetReq metricGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), MetricGetReq.class);
//        metricGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        metricGetReq.setEnterpriseNo("2000002");
        MetricRes metricRes = metricService.getMetric(metricGetReq);

        MetricVO metricVO = BeanCopyUtil.copyFields(metricRes, MetricVO.class);
        return metricVO;
    }

    /**
     * 查询指标
     */
    @GetMapping("/queryListPage")
    public PageVo<MetricVO> queryListPage(@RequestBody CommonDTO<MetricPageDTO> params) {
        MetricPageReq metricPageReq = BeanCopyUtil.copyFieldsByJson(params, MetricPageReq.class);
//        metricPageReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        metricPageReq.setEnterpriseNo("2000002");
        PageDto pageDTO = new PageDto();
        PageVo<MetricRes> metricResPageVo = metricService.queryListPage(metricPageReq, pageDTO);

        List<MetricVO> metricVOS = BeanCopyUtil.copyFieldsList(metricResPageVo.getRows(), MetricVO.class);
        PageVo<MetricVO> pageVo = new PageVo<>(metricResPageVo.getPageIndex(), metricResPageVo.getPageSize(), metricResPageVo.getTotal(), metricVOS);
        return pageVo;
    }
}