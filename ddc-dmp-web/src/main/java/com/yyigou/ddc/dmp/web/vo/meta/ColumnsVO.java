package com.yyigou.ddc.dmp.web.vo.meta;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ColumnsVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "表目录")
    private String tableCatalog;

    @EntityField(name = "表模式")
    private String tableSchema;

    @EntityField(name = "表名称")
    private String tableName;

    @EntityField(name = "列名称")
    private String columnName;

    @EntityField(name = "序号位置")
    private Long ordinalPosition;

    @EntityField(name = "列默认值")
    private String columnDefault;

    @EntityField(name = "是否可空")
    private String isNullable;

    @EntityField(name = "数据类型")
    private String dataType;

    @EntityField(name = "字符最大长度")
    private Long characterMaximumLength;

    @EntityField(name = "字符字节长度")
    private Long characterOctetLength;

    @EntityField(name = "数值精度")
    private Long numericPrecision;

    @EntityField(name = "数值标度")
    private Long numericScale;

    @EntityField(name = "日期时间精度")
    private Long datetimePrecision;

    @EntityField(name = "字符集名称")
    private String characterSetName;

    @EntityField(name = "排序规则名称")
    private String collationName;

    @EntityField(name = "列类型")
    private String columnType;

    @EntityField(name = "列键")
    private String columnKey;

    @EntityField(name = "额外信息")
    private String extra;

    @EntityField(name = "权限")
    private String privileges;
    @EntityField(name = "列注释")
    private String columnComment;

    @EntityField(name = "列大小")
    private Long columnSize;

    @EntityField(name = "小数位数")
    private Long decimalDigits;

    @EntityField(name = "生成表达式")
    private String generationExpression;

    @EntityField(name = "空间参考系统ID")
    private Long srsId;
}
