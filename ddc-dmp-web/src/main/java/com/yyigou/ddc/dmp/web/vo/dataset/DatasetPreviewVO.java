package com.yyigou.ddc.dmp.web.vo.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class DatasetPreviewVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户编号
     */
    @EntityField(name = "租户编号")
    private String enterpriseNo;

    /**
     * 数据集唯一标识
     */
    @EntityField(name = "数据集唯一标识")
    private String datasetNo;

    /**
     * 预览SQL
     */
    @EntityField(name = "预览SQL")
    private String sql;

    /**
     * 预览数据列表
     */
    @EntityField(name = "预览数据列表")
    private List<Map<String, Object>> data;

}
