package com.yyigou.ddc.dmp.web.api.dataset;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.dataset.*;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetPreviewVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetValidateVO;

import java.util.List;

public interface DatasetAPI extends ServiceBase {
    /**
     * 保存数据集
     *
     * @param params
     * @return
     */
    CallResult<DatasetVO> save(DatasetSaveDTO params);

    /**
     * 查询数据集
     *
     * @param params
     * @return
     */
    CallResult<PageVo<DatasetVO>> queryListPage(DatasetPageDTO params, PageDto pageParams);

    /**
     * 获取数据集
     *
     * @param params
     * @return
     */
    CallResult<DatasetVO> get(DatasetGetDTO params);

    /**
     * 获取数据集列表
     *
     * @param params
     * @return
     */
    CallResult<List<DatasetVO>> getList(DatasetGetListDTO params);

    /**
     * 预览数据集数据
     *
     * @param params
     * @return
     */
    CallResult<DatasetPreviewVO> previewData(DatasetPreviewDTO params);

    /**
     * 校验数据集配置
     *
     * @param params
     * @return
     */
    CallResult<DatasetValidateVO> validateDataset(DatasetValidateDTO params);

}
