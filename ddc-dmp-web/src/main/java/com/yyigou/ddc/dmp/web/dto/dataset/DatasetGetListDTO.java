package com.yyigou.ddc.dmp.web.dto.dataset;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class DatasetGetListDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模型唯一标识
     */
    @EntityField(name = "数据集唯一标识")
    private List<String> datasetNoList;

}
