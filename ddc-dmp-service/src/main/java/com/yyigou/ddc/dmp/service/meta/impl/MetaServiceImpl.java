package com.yyigou.ddc.dmp.service.meta.impl;

import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.meta.entity.Columns;
import com.yyigou.ddc.dmp.dao.meta.entity.Schemata;
import com.yyigou.ddc.dmp.dao.meta.entity.Tables;
import com.yyigou.ddc.dmp.dao.meta.mapper.MetaLoader;
import com.yyigou.ddc.dmp.model.res.meta.ColumnsRes;
import com.yyigou.ddc.dmp.model.res.meta.SchemataRes;
import com.yyigou.ddc.dmp.model.res.meta.TablesRes;
import com.yyigou.ddc.dmp.service.meta.MetaService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class MetaServiceImpl implements MetaService {
    @Resource
    private MetaLoader metaLoader;

    @Override
    public List<SchemataRes> loadSchematas() {
        List<Schemata> schematas = metaLoader.loadSchematas();

        return BeanCopyUtil.copyFieldsList(schematas, SchemataRes.class);
    }

    @Override
    public SchemataRes getSchemata(String schemaName) {
        Schemata schemata = metaLoader.getSchemata(schemaName);

        return BeanCopyUtil.copyFields(schemata, SchemataRes.class);
    }

    @Override
    public List<TablesRes> loadTables(String schemaName) {
        List<Tables> tables = metaLoader.loadTables(schemaName);

        return BeanCopyUtil.copyFieldsList(tables, TablesRes.class);
    }

    @Override
    public TablesRes getTable(String schemaName, String tableName) {
        Tables table = metaLoader.getTable(schemaName, tableName);

        return BeanCopyUtil.copyFields(table, TablesRes.class);
    }

    @Override
    public List<ColumnsRes> loadColumns(String schemaName, String tableName) {
        List<Columns> columns = metaLoader.loadColumns(schemaName, tableName);

        return BeanCopyUtil.copyFieldsList(columns, ColumnsRes.class);
    }

    @Override
    public List<ColumnsRes> loadColumnsByTables(String schemaName, List<String> tableNameList) {
        List<ColumnsRes> result = new ArrayList<>();
        for (String tableName : tableNameList) {
            List<ColumnsRes> columnsRes = loadColumns(schemaName, tableName);
            if (CollectionUtils.isNotEmpty(columnsRes)) {
                result.addAll(columnsRes);
            }
        }

        return result;
    }

    @Override
    public ColumnsRes getColumn(String schemaName, String tableName, String columnName) {
        Columns column = metaLoader.getColumn(schemaName, tableName, columnName);

        return BeanCopyUtil.copyFields(column, ColumnsRes.class);
    }
}