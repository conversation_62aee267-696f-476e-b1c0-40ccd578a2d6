package com.yyigou.ddc.dmp.model.req.subject;

import lombok.Data;

import java.io.Serializable;

@Data
public class SubjectStartStopReq implements Serializable {
    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 分析主题唯一标识
     */
    private String subjectNo;

    private Integer status;
//
//    /**
//     * 分析主题名称
//     */
//    private String subjectName;
//
//    /**
//     * 主题描述
//     */
//    private String description;
//
//    /**
//     * 数据是否有效：0-无效，1-有效
//     */
//    private Integer status;
//
//    /**
//     * 是否删除：0-未删除，1-已删除
//     */
//    private Integer deleted;
//
//    /**
//     * 创建人编号
//     */
//    private String createNo;
//
//    /**
//     * 创建人名称
//     */
//    private String createName;
//
//    /**
//     * 创建时间
//     */
//    private String createTime;
//
//    /**
//     * 修改人编号
//     */
//    private String modifyNo;
//
//    /**
//     * 修改人名称
//     */
//    private String modifyName;
//
//    /**
//     * 修改时间
//     */
//    private String modifyTime;
//
//    /**
//     * 最后操作时间
//     */
//    private LocalDateTime opTimestamp;
}
